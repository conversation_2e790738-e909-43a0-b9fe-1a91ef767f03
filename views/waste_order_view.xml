<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Form View for Waste Order -->
        <record id="view_waste_order_form" model="ir.ui.view">
            <field name="name">waste.order.form</field>
            <field name="model">waste.order</field>
            <field name="arch" type="xml">
                <form string="Waste Order">
                    <header>
                        <button string="Confirm" type="object" name="action_confirm"
                                visible="state == 'draft'"/>
                        <button name="action_done" type="object" string="Done"
                                visible="state == 'confirm'" class="oe_highlight"/>
                        <button string="Cancel" type="object" name="action_cancel"
                                visible="state in ('draft', 'confirm')"/>
                        <field name="state" widget="statusbar" />
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_credit_notes" type="object" class="oe_stat_button" icon="fa-pencil-square-o">
                                <field string="Credit Notes" name="credit_note_count" widget="statinfo"/>
                            </button>
                            <button name="action_view_scrap_orders" type="object" class="oe_stat_button" icon="fa-trash">
                                <field string="Scrap Orders" name="scrap_order_count" widget="statinfo" />
                            </button>
                        </div>
                        <h1>
                            <field name="name"/>
                        </h1>
                        <group>
                            <group>
                                <field name="partner_id"/>
                            </group>
                            <group>
                                <field name="date_order"/>
                                <field name="ref"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Order Lines">
                                <field name="waste_order_line_ids">
                                    <tree editable="bottom">
                                        <field name="product_id"/>
                                        <field name="description"/>
                                        <field name="tax_id" widget="many2many_tags"/>
                                        <field name="quantity"/>
                                        <field name="price_unit"/>
                                        <field name="price_subtotal"/>
                                    </tree>
                                </field>
                                <group name="note_group" col="6" class="mt-2 mt-md-0">
                                    <group colspan="4">
                                    </group>
                                    <group class="oe_subtotal_footer oe_right" colspan="2" name="sale_total">
                                        <field name="amount_untaxed" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                                        <field name="amount_tax" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                                        <div class="oe_subtotal_footer_separator oe_inline o_td_label">
                                            <label for="amount_total"/>
                                        </div>
                                        <field name="amount_total" nolabel="1" class="oe_subtotal_footer_separator" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                                    </group>
                                    <div class="oe_clear"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Tree View for Waste Order -->
        <record id="view_waste_order_tree" model="ir.ui.view">
            <field name="name">waste.order.tree</field>
            <field name="model">waste.order</field>
            <field name="arch" type="xml">
                <tree string="Waste Orders">
                    <field name="name"/>
                    <field name="date_order"/>
                    <field name="partner_id"/>
                    <field name="state"/>
                    <field name="amount_total" widget="monetary"/>
                </tree>
            </field>
        </record>

        <record id="action_waste_order" model="ir.actions.act_window">
            <field name="name">Waste Orders</field>
            <field name="res_model">waste.order</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_waste_order_tree"/>
        </record>

        <menuitem id="menu_waste_management"
                  name="Waste Management"
                  web_icon="excen_waste_management,static/description/icon2.png"
                  sequence="10"/>

        <menuitem id="menu_waste_order" name="Waste Orders"
                  parent="menu_waste_management"
                  action="action_waste_order"
                  sequence="10"/>

        <menuitem id="menu_waste_order_reports" name="Reports"
                  parent="menu_waste_management"
                  sequence="10"/>


    </data>
</odoo>