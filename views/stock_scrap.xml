<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="action_scrap_order_validate" model="ir.actions.server">
            <field name="name">Validate Scrap Orders</field>
            <field name="model_id" ref="stock.model_stock_scrap"/>
            <field name="binding_model_id" ref="stock.model_stock_scrap"/>
            <field name="state">code</field>
            <field name="code">
                action = records.action_validate()
            </field>
        </record>

        <record id="view_scrap_order_tree_button" model="ir.ui.view">
            <field name="name">scrap.order.tree.button</field>
            <field name="model">stock.scrap</field>
            <field name="inherit_id" ref="stock.stock_scrap_tree_view"/>
            <field name="arch" type="xml">
                <xpath expr="//tree" position="inside">
                    <header>
                        <button name="%(action_scrap_order_validate)d" string="Validate Orders" type="action" class="oe_highlight" context="{'default_model': 'stock.scrap'}"/>
                    </header>
                </xpath>
            </field>
        </record>
    </data>
</odoo>