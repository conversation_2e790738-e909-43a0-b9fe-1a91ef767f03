<odoo>
    <!-- Define Custom Security Category -->
    <record id="module_category_waste_management" model="ir.module.category">
        <field name="name">Waste Management</field>
        <field name="description">Manage waste orders and related activities</field>
        <field name="sequence">10</field>
    </record>

    <!-- Define User Group -->
    <record id="group_waste_user" model="res.groups">
        <field name="name">Waste Management User</field>
        <field name="category_id" ref="excen_waste_management.module_category_waste_management"/>
    </record>

    <!-- Define Manager Group -->
    <record id="group_waste_manager" model="res.groups">
        <field name="name">Waste Management Manager</field>
        <field name="category_id" ref="excen_waste_management.module_category_waste_management"/>
    </record>

    <!-- Access Rights for Waste Orders -->
    <record id="access_waste_order_user" model="ir.model.access">
        <field name="name">waste.order user access</field>
        <field name="model_id" ref="model_waste_order"/>
        <field name="group_id" ref="excen_waste_management.group_waste_user"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_create" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    <record id="access_waste_order_manager" model="ir.model.access">
        <field name="name">waste.order manager access</field>
        <field name="model_id" ref="model_waste_order"/>
        <field name="group_id" ref="excen_waste_management.group_waste_manager"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_unlink" eval="1"/>
    </record>

    <!-- Access Rights for Waste Order Lines -->
    <record id="access_waste_order_line_user" model="ir.model.access">
        <field name="name">waste.order.line user access</field>
        <field name="model_id" ref="model_waste_order_line"/>
        <field name="group_id" ref="excen_waste_management.group_waste_user"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_create" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    <record id="access_waste_order_line_manager" model="ir.model.access">
        <field name="name">waste.order.line manager access</field>
        <field name="model_id" ref="model_waste_order_line"/>
        <field name="group_id" ref="excen_waste_management.group_waste_manager"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_unlink" eval="1"/>
    </record>
</odoo>
