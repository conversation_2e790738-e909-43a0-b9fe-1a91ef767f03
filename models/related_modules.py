from odoo import models, fields, api

class StockScrap(models.Model):
    _inherit = 'stock.scrap'

    waste_order_id = fields.Many2one('waste.order', string='Waste Order')

    def action_validate(self):
        for scrap_order in self:
            if scrap_order.state == 'draft':
                scrap_order.do_scrap()


class AccountMove(models.Model):
    _inherit = 'account.move'

    waste_order_id = fields.Many2one('waste.order', string='Waste Order')