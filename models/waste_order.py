from odoo import fields, models, api, _


class WasteOrder(models.Model):
    _name = 'waste.order'
    _description = 'Waste Order'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Number', required=True, copy=False, readonly=True, index=True, default=lambda self: _('New'))
    date_order = fields.Date(string='Order Date', required=True, default=fields.Date.context_today)
    partner_id = fields.Many2one('res.partner', string='Customer', required=True)
    ref = fields.Char(string='Order Reference')
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirm', 'Confirmed'),
        ('done', 'Done'),
        ('cancel', 'Cancelled')
    ], string='Status', readonly=True, index=True, copy=False, default='draft', tracking=True)
    waste_order_line_ids = fields.One2many('waste.order.line', 'order_id', string='Waste Order Lines', copy=True)
    amount_untaxed = fields.Monetary(string='Untaxed Amount', store=True, readonly=True, compute='_amount_all')
    amount_tax = fields.Monetary(string='Taxes', store=True, readonly=True, compute='_amount_all')
    amount_total = fields.Monetary(string='Total', store=True, readonly=True, compute='_amount_all')
    currency_id = fields.Many2one('res.currency', string='Currency', required=True, default=lambda self: self.env.user.company_id.currency_id)
    scrap_order_ids = fields.One2many('stock.scrap', 'waste_order_id', string='Related Waste Orders')
    scrap_order_count = fields.Integer(string="Scrap Orders", compute="_compute_scrap_order_count")
    credit_note_ids = fields.One2many('account.move', 'waste_order_id', string='Credit Notes')
    credit_note_count = fields.Integer(string="Credit Notes", compute="_compute_credit_note_count")
    company_id = fields.Many2one('res.company', string='Company', required=True, default=lambda self: self.env.company)

    @api.model
    def create(self, vals):
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('waste.order') or _('New')
        return super(WasteOrder, self).create(vals)

    @api.depends('waste_order_line_ids.price_subtotal')
    def _amount_all(self):
        for order in self:
            amount_untaxed = amount_tax = 0.0
            for line in order.waste_order_line_ids:
                amount_untaxed += line.price_subtotal
                amount_tax += line.price_tax #* line.price_subtotal / 100
            order.update({
                'amount_untaxed': amount_untaxed,
                'amount_tax': amount_tax,
                'amount_total': amount_untaxed + amount_tax,
            })

    def action_confirm(self):
        self.write({'state': 'confirm'})
        self.create_credit_note()
        self.create_scrap_order()

    def action_done(self):
        self.write({'state': 'done'})

    def action_cancel(self):
        self.write({'state': 'cancelled'})

    def create_scrap_order(self):
        scrap_obj = self.env['stock.scrap']
        for order in self:
            for line in order.waste_order_line_ids:
                scrap = scrap_obj.create({
                    'owner_id': order.partner_id.id,
                    'product_id': line.product_id.id,
                    'scrap_qty': line.quantity,
                    'product_uom_id': line.product_id.uom_id.id,
                    'location_id': self.env.ref('stock.stock_location_stock').id,  # Default location, adjust as needed
                    'company_id': order.company_id.id,
                    'waste_order_id': order.id,
                })
                line.scrap_id = scrap.id

    def _compute_scrap_order_count(self):
        for order in self:
            order.scrap_order_count = self.env['stock.scrap'].search_count([('waste_order_id', '=', order.id)])

    def action_view_scrap_orders(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Scrap Orders',
            'view_mode': 'tree,form',
            'res_model': 'stock.scrap',
            'domain': [('waste_order_id', '=', self.id)],
            'context': dict(self.env.context, create=False),
        }

    def create_credit_note(self):
        # Create a credit note for the confirmed order
        credit_note_vals = {
            'move_type': 'out_refund',
            'partner_id': self.partner_id.id,
            'invoice_date': fields.Date.today(),
            'invoice_origin': self.name,
            'waste_order_id': self.id,
            'invoice_line_ids': [(0, 0, {
                'product_id': line.product_id.id,
                'quantity': line.quantity,
                'price_unit': line.price_unit,
                'tax_ids': [(6, 0, line.tax_id.ids)],
            }) for line in self.waste_order_line_ids],
        }
        self.env['account.move'].create(credit_note_vals)

    def _compute_credit_note_count(self):
        for order in self:
            order.credit_note_count = self.env['account.move'].search_count(
                [('waste_order_id', '=', order.id), ('move_type', '=', 'out_refund')])

    def action_view_credit_notes(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Credit Notes',
            'view_mode': 'tree,form',
            'res_model': 'account.move',
            'domain': [('waste_order_id', '=', self.id), ('move_type', '=', 'out_refund')],
            'context': dict(self.env.context, create=False),
        }

class WasteOrderLine(models.Model):
    _name = 'waste.order.line'
    _description = 'Waste Order Line'

    order_id = fields.Many2one('waste.order', string='Number', required=True, ondelete='cascade', index=True)
    product_id = fields.Many2one('product.product', string='Product', required=True)
    description = fields.Text(string='Description')
    quantity = fields.Float(string='Quantity', required=True, default=1.0)
    tax_id = fields.Many2many('account.tax', string='Taxes')
    price_unit = fields.Float(string='Unit Price', required=True)
    price_subtotal = fields.Monetary(string='Subtotal', compute='_compute_price', store=True)
    price_tax = fields.Monetary(string='Tax', compute='_compute_price', store=True)
    price_total = fields.Monetary(string='Total', compute='_compute_price', store=True)
    currency_id = fields.Many2one(related='order_id.currency_id', store=True, string='Currency')
    scrap_id = fields.Many2one('stock.scrap', string='Scrap Order', ondelete='cascade')

    @api.onchange('product_id', 'quantity')
    def _onchange_product_id(self):
        if self.product_id:
            order = self.order_id
            partner = order.partner_id
            pricelist = partner.property_product_pricelist

            # Apply the pricelist to get the correct price
            price = pricelist._get_product_price(self.product_id, self.quantity or 1.0)
            self.price_unit = price

            # Get the default taxes and description from the product
            self.tax_id = self.product_id.taxes_id
            self.description = self.product_id.description_sale
            # self.price_unit = self.product_id.list_price
            # self.tax_id = self.product_id.taxes_id
            # self.description = self.product_id.description_sale

    @api.depends('quantity', 'price_unit', 'tax_id')
    def _compute_price(self):
        for line in self:
            price = line.price_unit * line.quantity
            taxes = line.tax_id.compute_all(line.price_unit, line.order_id.currency_id, line.quantity, product=line.product_id,
                                            partner=line.order_id.partner_id)
            line.update({
                'price_subtotal': taxes['total_excluded'],
                'price_tax': taxes['total_included'] - taxes['total_excluded'],
                'price_total': taxes['total_included'],
            })
            # price = line.price_unit * line.quantity
            # taxes = line.tax_id.compute_all(price, line.order_id.currency_id, line.quantity, product=line.product_id, partner=line.order_id.partner_id)
            # line.update({
            #     'price_subtotal': taxes['total_excluded'],
            #     'price_tax': taxes['total_included'] - taxes['total_excluded'],
            #     'price_total': taxes['total_included'],
            # })
