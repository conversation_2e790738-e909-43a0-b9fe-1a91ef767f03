from odoo import models, fields, api

class WasteOrderReportWizard(models.TransientModel):
    _name = 'waste.order.report.wizard'
    _description = 'Waste Order Report Wizard'

    date_from = fields.Date(string="Start Date", required=True)
    date_to = fields.Date(string="End Date", required=True)
    partner_id = fields.Many2one('res.partner', string="Partner")

    def action_print_report(self):
        data = {
            'date_from': self.date_from,
            'date_to': self.date_to,
            'partner_id': self.partner_id.id,
        }
        # return self.env.ref('excen_waste_management.waste_order_report').report_action(self, data=data)
