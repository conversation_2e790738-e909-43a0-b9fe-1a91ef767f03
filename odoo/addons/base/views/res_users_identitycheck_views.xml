<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="res_users_identitycheck_view_form" model="ir.ui.view">
            <field name="model">res.users.identitycheck</field>
            <field name="arch" type="xml">
                <form string="Password Confirmation">
                    <sheet>
                        <div invisible="auth_method != 'password'">
                            <h3><strong>Please confirm your identity by entering your password</strong></h3>
                            <div>
                                <field class="o_field_highlight col-10 col-md-6 px-0" name="password" autocomplete="current-password"
                                    required="auth_method == 'password'" password="True" placeholder="************"/>
                            </div>
                            <a href="/web/reset_password/" class="btn btn-link" role="button">Forgot password?</a>
                        </div>
                    </sheet>
                    <footer>
                        <button string="Confirm Password" id="password_confirm" type="object" name="run_check" class="btn btn-primary" data-hotkey="q" invisible="auth_method != 'password'"/>
                        <button string="Cancel" special="cancel" data-hotkey="z" class="btn btn-secondary"/>
                    </footer>
                </form>
            </field>
        </record>

    </data>
</odoo>
