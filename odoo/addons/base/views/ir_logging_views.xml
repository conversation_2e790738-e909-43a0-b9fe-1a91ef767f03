<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record model="ir.ui.view" id="ir_logging_form_view">
            <field name="model">ir.logging</field>
            <field name="arch" type="xml">
                <form string="Log">
                <sheet>
                    <group name="creation_details" string="Creation details">
                        <field name="create_date" />
                        <field name="create_uid" />
                        <field name="dbname" />
                    </group>
                    <group name="log_details" string="Logging details">
                        <field name="type" />
                        <field name="name" />
                        <field name="level" />
                        <field name="path" />
                        <field name="line" />
                        <field name="func" />
                        <field name="message" />
                    </group>
                </sheet>
                </form>
            </field>
        </record>
        <record model="ir.ui.view" id="ir_logging_tree_view">
            <field name="model">ir.logging</field>
            <field name="arch" type="xml">
                <list string="Logs">
                    <field name="create_date" />
                    <field name="create_uid" />
                    <field name="dbname" />
                    <field name="type" />
                    <field name="name" />
                    <field name="level" />
                    <field name="path" />
                    <field name="line" />
                    <field name="func" />
                </list>
            </field>
        </record>
        <record model="ir.ui.view" id="ir_logging_search_view">
            <field name="model">ir.logging</field>
            <field name="arch" type="xml">
                <search string="Logs">
                    <field name="dbname" />
                    <field name="type" />
                    <field name="name" />
                    <field name="level" />
                    <field name="message" />
                    <group expand="0" string="Group By">
                        <filter string="Database" name="database" domain="[]" context="{'group_by': 'dbname'}" />
                        <filter string="Level" name="group_by_level" domain="[]" context="{'group_by': 'level'}" />
                        <filter string="Type" name="group_by_type" domain="[]" context="{'group_by': 'type'}" />
                        <filter string="Creation Date" name="group_by_month" domain="[]" context="{'group_by': 'create_date'}" />
                    </group>
                </search>
            </field>
        </record>
        <record model="ir.actions.act_window" id="ir_logging_all_act">
            <field name="name">Logging</field>
            <field name="res_model">ir.logging</field>
            <field name="view_mode">list,form</field>
            <field name="search_view_id" ref="ir_logging_search_view" />
        </record>
        <menuitem parent="base.next_id_9" id="ir_logging_all_menu" action="ir_logging_all_act" />
</odoo>
