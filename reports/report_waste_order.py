from odoo import models, api


class ReportWasteOrder(models.AbstractModel):
    _name = 'report.excen_waste_management.report_waste_order'
    _description = 'Waste Order Report'

    @api.model
    def _get_report_values(self, docids, data=None):
        date_from = data['date_from']
        date_to = data['date_to']
        partner_id = data['partner_id']

        domain = [('date_order', '>=', date_from), ('date_order', '<=', date_to)]
        if partner_id:
            domain.append(('partner_id', '=', partner_id))

        orders = self.env['waste.order'].search(domain)

        return {
            'doc_ids': docids,
            'doc_model': 'waste.order',
            'docs': orders,
            'data': data,
        }
