# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_fiscal_year
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-15 18:18+0000\n"
"PO-Revision-Date: 2022-04-15 18:18+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: om_fiscal_year
#: model_terms:ir.ui.view,arch_db:om_fiscal_year.view_change_lock_date
msgid "Account Period Closing"
msgstr ""

#. module: om_fiscal_year
#: model:res.groups,name:om_fiscal_year.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr "تسمح بتحديد السنوات المالية التي تزيد أو تقل عن عام\n"

#. module: om_fiscal_year
#: model_terms:ir.ui.view,arch_db:om_fiscal_year.view_change_lock_date
msgid "Cancel"
msgstr ""

#. module: om_fiscal_year
#: model:ir.model,name:om_fiscal_year.model_change_lock_date
msgid "Change Lock Date"
msgstr "تغيير تاريخ القفل\n"

#. module: om_fiscal_year
#: model_terms:ir.actions.act_window,help:om_fiscal_year.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr "انقر هنا لإنشاء سنة مالية جديدة.\n"

#. module: om_fiscal_year
#: model:ir.model,name:om_fiscal_year.model_res_company
msgid "Companies"
msgstr "شركات"

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_account_fiscal_year__company_id
#: model:ir.model.fields,field_description:om_fiscal_year.field_change_lock_date__company_id
msgid "Company"
msgstr "الشركة"

#. module: om_fiscal_year
#: model:ir.model,name:om_fiscal_year.model_res_config_settings
msgid "Config Settings"
msgstr "ضبط الاعدادات"

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_account_fiscal_year__create_uid
#: model:ir.model.fields,field_description:om_fiscal_year.field_change_lock_date__create_uid
msgid "Created by"
msgstr ""

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_account_fiscal_year__create_date
#: model:ir.model.fields,field_description:om_fiscal_year.field_change_lock_date__create_date
msgid "Created on"
msgstr ""

#. module: om_fiscal_year
#: model_terms:ir.ui.view,arch_db:om_fiscal_year.res_config_settings_view_form
msgid "Define fiscal years of more or less than one year"
msgstr "تحديد السنوات المالية التي تزيد أو تقل عن سنة واحدة\n"

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_account_fiscal_year__display_name
#: model:ir.model.fields,field_description:om_fiscal_year.field_change_lock_date__display_name
msgid "Display Name"
msgstr ""

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_account_fiscal_year__date_to
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: om_fiscal_year
#: model:ir.model.fields,help:om_fiscal_year.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr ""

#. module: om_fiscal_year
#: model_terms:ir.ui.view,arch_db:om_fiscal_year.res_config_settings_view_form
msgid "Fiscal Period Closing"
msgstr "إغلاق الفترة المالية\n"

#. module: om_fiscal_year
#: model:ir.model,name:om_fiscal_year.model_account_fiscal_year
#: model:ir.ui.menu,name:om_fiscal_year.menu_fiscal_year
#: model_terms:ir.ui.view,arch_db:om_fiscal_year.res_config_settings_view_form
msgid "Fiscal Year"
msgstr "السنة المالية\n"

#. module: om_fiscal_year
#: model:ir.actions.act_window,name:om_fiscal_year.actions_account_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_res_config_settings__group_fiscal_year
#: model_terms:ir.ui.view,arch_db:om_fiscal_year.res_config_settings_view_form
msgid "Fiscal Years"
msgstr "السنوات المالية"

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "آخر أيام السنة المالية"

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr ""

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_account_fiscal_year__id
#: model:ir.model.fields,field_description:om_fiscal_year.field_change_lock_date__id
msgid "ID"
msgstr ""

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_change_lock_date__period_lock_date
msgid "Journal Entries Lock Date"
msgstr "تاريخ قفل إدخالات دفتر اليومية\n"

#. module: om_fiscal_year
#: model_terms:ir.ui.view,arch_db:om_fiscal_year.res_config_settings_view_form
msgid "Last Day"
msgstr "بالأمس"

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_account_fiscal_year____last_update
#: model:ir.model.fields,field_description:om_fiscal_year.field_change_lock_date____last_update
msgid "Last Modified on"
msgstr ""

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_account_fiscal_year__write_uid
#: model:ir.model.fields,field_description:om_fiscal_year.field_change_lock_date__write_uid
msgid "Last Updated by"
msgstr ""

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_account_fiscal_year__write_date
#: model:ir.model.fields,field_description:om_fiscal_year.field_change_lock_date__write_date
msgid "Last Updated on"
msgstr ""

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_res_config_settings__fiscalyear_lock_date
msgid "Lock Date"
msgstr "تاريخ القفل\n"

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_change_lock_date__fiscalyear_lock_date
msgid "Lock Date for All Users"
msgstr "تاريخ القفل لجميع المستخدمين\n"

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_res_config_settings__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "تاريخ القفل لغير المستشارين\n"

#. module: om_fiscal_year
#: model:ir.ui.menu,name:om_fiscal_year.menu_action_change_lock_date
msgid "Lock Dates"
msgstr "تواريخ القفل\n"

#. module: om_fiscal_year
#: model:ir.actions.act_window,name:om_fiscal_year.action_view_change_lock_date
msgid "Lock your Fiscal Period"
msgstr "قفل الفترة المالية الخاصة بك\n"

#. module: om_fiscal_year
#: model_terms:ir.ui.view,arch_db:om_fiscal_year.res_config_settings_view_form
msgid "Lock your fiscal period"
msgstr "قفل الفترة المالية الخاصة بك\n"

#. module: om_fiscal_year
#: model_terms:ir.ui.view,arch_db:om_fiscal_year.view_change_lock_date
msgid "Management Closing"
msgstr ""

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_account_fiscal_year__name
msgid "Name"
msgstr ""

#. module: om_fiscal_year
#: model:ir.model.fields,help:om_fiscal_year.field_change_lock_date__tax_lock_date
msgid ""
"No users can edit journal entries related to a tax prior and inclusive of "
"this date."
msgstr ""

#. module: om_fiscal_year
#: model:ir.model.fields,help:om_fiscal_year.field_res_config_settings__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""

#. module: om_fiscal_year
#: model:ir.model.fields,help:om_fiscal_year.field_change_lock_date__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking."
msgstr ""

#. module: om_fiscal_year
#: model:ir.model.fields,help:om_fiscal_year.field_res_config_settings__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""

#. module: om_fiscal_year
#: model:ir.model.fields,help:om_fiscal_year.field_change_lock_date__period_lock_date
msgid "Prevent posting of journal entries in this period."
msgstr ""

#. module: om_fiscal_year
#: model_terms:ir.ui.view,arch_db:om_fiscal_year.view_change_lock_date
msgid "Save"
msgstr ""

#. module: om_fiscal_year
#: code:addons/om_fiscal_year/models/res_company.py:0
#, python-format
msgid "Show unposted entries"
msgstr ""

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_account_fiscal_year__date_from
msgid "Start Date"
msgstr "تاريخ البداية"

#. module: om_fiscal_year
#: model:ir.model.fields,help:om_fiscal_year.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr "تاريخ البدء مضمن في السنة المالية.\n"

#. module: om_fiscal_year
#: model:ir.model.fields,field_description:om_fiscal_year.field_change_lock_date__tax_lock_date
msgid "Tax Lock Date"
msgstr "تاريخ القفل الضريبي\n"

#. module: om_fiscal_year
#: code:addons/om_fiscal_year/models/account_fiscal_year.py:0
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr ""

#. module: om_fiscal_year
#: code:addons/om_fiscal_year/models/res_company.py:0
#, python-format
msgid ""
"There are still unposted entries in the period you want to lock. You should "
"either post or delete them."
msgstr ""

#. module: om_fiscal_year
#: code:addons/om_fiscal_year/models/res_company.py:0
#, python-format
msgid ""
"There are still unreconciled bank statement lines in the period you want to "
"lock.You should either reconcile or delete them."
msgstr ""

#. module: om_fiscal_year
#: code:addons/om_fiscal_year/wizard/change_lock_date.py:0
#, python-format
msgid "You Are Not Allowed To Perform This Operation"
msgstr ""

#. module: om_fiscal_year
#: code:addons/om_fiscal_year/models/account_fiscal_year.py:0
#, python-format
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""
"لا يمكن أن يكون هناك تداخل بين سنتين ماليتين ، يرجى تصحيح تواريخ البدء و / "
"أو الانتهاء للسنتين الماليتين.\n"
