# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* html_editor
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <erial<PERSON><PERSON>@gmail.com>, 2024
# mart<PERSON><PERSON>o hola, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Jonatan Gk, 2024
# jabiri7, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# Martin <PERSON>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:37+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_description.xml:0
msgid ""
"'Alt tag' specifies an alternate text for an image, if the image cannot be "
"displayed (slow connection, missing image, screen reader ...)."
msgstr ""
"L'etiqueta '*Alt' especifica un text alternatiu per a una imatge, si la "
"imatge no pot mostrar-se (connexió lenta, falta d'imatge, lector de "
"pantalla...)."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_description.xml:0
msgid "'Title tag' is shown as a tooltip when you hover the picture."
msgstr ""
"L'\"etiqueta del títol\" es mostra com un tooltip quan es passa per sobre de"
" la imatge."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_description.xml:0
msgid "(ALT Tag)"
msgstr "(Etiqueta ALT)"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_description.xml:0
msgid "(TITLE Tag)"
msgstr "(Etiqueta Títol)"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.xml:0
msgid "(URL or Embed)"
msgstr "(URL o codi incrustat)"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/column_plugin.js:0
msgid "2 columns"
msgstr "2 columnes"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/star_plugin.js:0
msgid "3 Stars"
msgstr "3 estrelles"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/column_plugin.js:0
msgid "3 columns"
msgstr "3 columnes"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/column_plugin.js:0
msgid "4 columns"
msgstr "4 columnes"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/star_plugin.js:0
msgid "5 Stars"
msgstr "5 estrelles"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_plugin.js:0
msgid "AI"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_alternatives_dialog.xml:0
msgid "AI Copywriter"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_plugin.js:0
msgid "AI Tools"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.xml:0
msgid "Accepts"
msgstr "Accepta"

#. module: html_editor
#. odoo-python
#: code:addons/html_editor/controllers/main.py:0
msgid "Action %s is not a window action, link preview is not available"
msgstr ""

#. module: html_editor
#. odoo-python
#: code:addons/html_editor/controllers/main.py:0
msgid ""
"Action %s not found, link preview is not available, please check your url is"
" correct"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/media_dialog.xml:0
msgid "Add"
msgstr "Afegir"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/document_selector.js:0
#: code:addons/html_editor/static/src/main/media/media_dialog/image_selector.js:0
msgid "Add URL"
msgstr "Afegir un URL"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Add a blockquote section"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_plugin.js:0
msgid "Add a button"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Add a code section"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/file_plugin.js:0
msgid "Add a download box"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_plugin.js:0
msgid "Add a link"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/emoji_plugin.js:0
msgid "Add an emoji"
msgstr "Afegir un emoji"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/embedded_components/core/table_of_content/table_of_content.xml:0
msgid "Add headings in this field to fill the Table of Content"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.js:0
msgid "Alert"
msgstr "Alerta"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.xml:0
msgid "All"
msgstr "Tots"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/document_selector.js:0
msgid "All documents have been loaded"
msgstr "Tots els documents han sigut carregats"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/image_selector.js:0
msgid "All images have been loaded"
msgstr "Totes les imatges s'han carregat"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.js:0
msgid "An error occurred while fetching the entered URL."
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/gradient_picker.xml:0
msgid "Angle"
msgstr "Angle"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/collaboration/collaboration_selection_avatar_plugin.js:0
#: code:addons/html_editor/static/src/others/collaboration/collaboration_selection_plugin.js:0
msgid "Anonymous"
msgstr "Anònim"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
#: code:addons/html_editor/static/src/main/media/image_crop.xml:0
msgid "Apply"
msgstr "Aplica"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.js:0
msgid "Are you sure you want to delete this file?"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_crop.xml:0
msgid "Aspect Ratio"
msgstr "Relació d'aspecte"

#. module: html_editor
#: model:ir.model,name:html_editor.model_ir_attachment
msgid "Attachment"
msgstr "Adjunt"

#. module: html_editor
#: model:ir.model.fields,field_description:html_editor.field_ir_attachment__local_url
msgid "Attachment URL"
msgstr "L'URL de l'adjunt"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.js:0
msgid "Autoplay"
msgstr "Reproducció automàtica"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/column_plugin.js:0
msgid "Back to one column"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/color_plugin.js:0
#: code:addons/html_editor/static/src/main/media/icon_plugin.js:0
msgid "Background Color"
msgstr "Color de fons"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/banner_plugin.js:0
msgid "Banner"
msgstr "Bàner"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/html_migrations/migration-1.2.js:0
#: code:addons/html_editor/static/src/main/banner_plugin.js:0
msgid "Banner Danger"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/html_migrations/migration-1.2.js:0
#: code:addons/html_editor/static/src/main/banner_plugin.js:0
msgid "Banner Info"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/html_migrations/migration-1.2.js:0
#: code:addons/html_editor/static/src/main/banner_plugin.js:0
msgid "Banner Success"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/html_migrations/migration-1.2.js:0
#: code:addons/html_editor/static/src/main/banner_plugin.js:0
msgid "Banner Warning"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/signature_plugin.js:0
msgid "Basic Bloc"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Big section heading"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/list/list_plugin.js:0
msgid "Bulleted list"
msgstr "Llista de vinyetes"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_plugin.js:0
msgid "Button"
msgstr "Botó "

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.js:0
msgid "Button Primary"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.js:0
msgid "Button Secondary"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_alternatives_dialog.xml:0
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_translate_dialog.xml:0
#: code:addons/html_editor/static/src/others/embedded_components/plugins/video_plugin/video_selector_dialog/video_selector_dialog.xml:0
msgid "Cancel"
msgstr "Cancel·la"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_description.xml:0
msgid "Change media description and tooltip"
msgstr "Canviar la descripció del contingut multimèdia i el tooltip"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_plugin.js:0
msgid "ChatGPT"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/list/list_plugin.js:0
msgid "Checklist"
msgstr "Llista de verificació"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/x2many_image_field.xml:0
msgid "Clear"
msgstr "Esborrar"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/upload_progress_toast/upload_progress_toast.xml:0
#: code:addons/html_editor/static/src/others/embedded_components/core/file/readonly_file.js:0
msgid "Close"
msgstr "Tancar"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Code"
msgstr "Codi"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/fields/html_field.js:0
msgid "Code view"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/column_plugin.js:0
msgid "Columnize"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/components/history_dialog/history_dialog.js:0
msgid "Comparison"
msgstr "Comparació"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/components/history_dialog/history_dialog.js:0
msgid "Content"
msgstr "Contingut"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_dialog.js:0
msgid "Content generated"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/column_plugin.js:0
msgid "Convert into 2 columns"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/column_plugin.js:0
msgid "Convert into 3 columns"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/column_plugin.js:0
msgid "Convert into 4 columns"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/column_plugin.js:0
msgid "Convert into columns"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "Copy Link"
msgstr "Copiar enllaç"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.xml:0
msgid "Copy-paste your URL or embed code here"
msgstr "Copia l'URL o el codi incrustat aquí"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_alternatives_dialog.js:0
msgid "Correct"
msgstr "Correcte"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/upload_progress_toast/upload_service.js:0
msgid "Could not load the file \"%s\"."
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/list/list_plugin.js:0
msgid "Create a list with numbering"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/list/list_plugin.js:0
msgid "Create a simple bulleted list"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_plugin.js:0
msgid "Create an URL."
msgstr "Crear una URL."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_crop_plugin.js:0
msgid "Crop image"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/color_selector.xml:0
#: code:addons/html_editor/static/src/main/link/link_popover.js:0
msgid "Custom"
msgstr "Personalitzat"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.xml:0
msgid "Dailymotion"
msgstr "Moviment diari"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Default"
msgstr "Per defecte"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/color_selector.xml:0
msgid "Define a custom gradient"
msgstr "Definir un gradient personalitzat"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/table/table_menu.js:0
msgid "Delete"
msgstr "Eliminar"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_description.xml:0
msgid "Description"
msgstr "Descripció"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/components/history_dialog/history_dialog.xml:0
#: code:addons/html_editor/static/src/main/media/image_crop.xml:0
#: code:addons/html_editor/static/src/main/media/image_description.xml:0
#: code:addons/html_editor/static/src/main/media/media_dialog/media_dialog.xml:0
msgid "Discard"
msgstr "Descartar"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/image_selector.xml:0
msgid ""
"Discover a world of awesomeness in our copyright-free image haven. No legal "
"drama, just nice images!"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/file_plugin.js:0
#: code:addons/html_editor/static/src/main/media/media_dialog/file_media_dialog.js:0
msgid "Documents"
msgstr "Documents"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/dynamic_placeholder_plugin.js:0
msgid "Dynamic Placeholder"
msgstr "Placeholder dinàmic"

#. module: html_editor
#. odoo-python
#: code:addons/html_editor/controllers/main.py:0
msgid "ERROR: couldn't get download urls from media library."
msgstr ""
"ERROR: no s'han pogut obtenir les urls de descàrrega de la llibreria del "
"contingut multimèdia."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/x2many_image_field.xml:0
msgid "Edit"
msgstr "Modificar"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "Edit Link"
msgstr "Modificar enllaç"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Edit media description"
msgstr "Modificar la descripció del contingut multimèdia"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Embed Image"
msgstr "Inserir imatge"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/youtube_plugin.js:0
msgid "Embed Youtube Video"
msgstr "Inserir vídeo de YouTube"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/embedded_components/plugins/video_plugin/video_selector_dialog/video_selector_dialog.xml:0
msgid "Embed a video"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Embed the image in the document."
msgstr "Inserir la imatge al document."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/youtube_plugin.js:0
msgid "Embed the youtube video in the document."
msgstr "Inserir el vídeo de YouTube al document."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/emoji_plugin.js:0
msgid "Emoji"
msgstr "Emoji"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/column_plugin.js:0
msgid "Empty column"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.js:0
msgid "Error"
msgstr "Error"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/gradient_picker.xml:0
msgid "Extend to the closest corner"
msgstr "Estendre fins a la cantonada més propera"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/gradient_picker.xml:0
msgid "Extend to the closest side"
msgstr "Estendre fins al costat més proper"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/gradient_picker.xml:0
msgid "Extend to the farthest corner"
msgstr "Estendre fins a la cantonada més llunyana"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/gradient_picker.xml:0
msgid "Extend to the farthest side"
msgstr "Estendre fins al costat més llunyà"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/embedded_components/plugins/file_plugin/file_plugin.js:0
msgid "File"
msgstr "Fitxer"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/upload_progress_toast/upload_progress_toast.xml:0
msgid "File has been uploaded"
msgstr "L'arxiu ha sigut carregat"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.js:0
msgid "Fill"
msgstr "Omplir"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.js:0
msgid "Fill + Rounded"
msgstr "Omplir + Arrodoniment"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/gradient_picker.xml:0
msgid "First color %"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_crop.js:0
msgid "Flexible"
msgstr "Flexible"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_crop.xml:0
msgid "Flip Horizontal"
msgstr "Voltejar horitzontalment"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_crop.xml:0
msgid "Flip Vertical"
msgstr "Voltejar verticalment"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/color_plugin.js:0
#: code:addons/html_editor/static/src/main/media/icon_plugin.js:0
msgid "Font Color"
msgstr "Color de la font"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Font size"
msgstr "Grandària de lletra"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Font style"
msgstr "Tipus de lletra"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Format"
msgstr "Formata"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_alternatives_dialog.js:0
msgid "Friendly"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_prompt_dialog.xml:0
msgid "Generate Text with AI"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_plugin.js:0
msgid "Generate or transform content with AI."
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_alternatives_dialog.xml:0
msgid "Generating"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_alternatives_dialog.xml:0
msgid "Generating an alternative..."
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/color_selector.xml:0
msgid "Gradient"
msgstr "Gradient"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Header 1"
msgstr "Capçalera 1"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Header 1 Display 1"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Header 2"
msgstr "Capçalera 2"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Header 3"
msgstr "Capçalera 3"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Header 4"
msgstr "Capçalera 4"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Header 5"
msgstr "Capçalera 5"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Header 6"
msgstr "Capçalera 6"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Heading 1"
msgstr "Encapçalament 1"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Heading 2"
msgstr "Encapçalament 2"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Heading 3"
msgstr "Encapçalament 3"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Heading 4"
msgstr "Encapçalament 4"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Heading 5"
msgstr "Encapçalament 5"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Heading 6"
msgstr "Encapçalament 6"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.js:0
msgid "Hide Dailymotion logo"
msgstr "Amagar el logo de Dailymotion"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.js:0
msgid "Hide fullscreen button"
msgstr "Amagar el botó de pantalla completa"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.js:0
msgid "Hide player controls"
msgstr "Amagar controls del jugador"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.js:0
msgid "Hide sharing button"
msgstr "Amagar el botó de compartir"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/embedded_components/plugins/table_of_content_plugin/table_of_content_plugin.js:0
msgid "Highlight the structure (headings) of this field"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/components/history_dialog/history_dialog.js:0
msgid "History"
msgstr "Historial"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/fields/html_field.js:0
msgid "Html"
msgstr "Html"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_plugin.js:0
msgid "Icon"
msgstr "Icona"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/icon_plugin.js:0
msgid "Icon size 1x"
msgstr "Mida d'icona 1x"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/icon_plugin.js:0
msgid "Icon size 2x"
msgstr "Mida d'icona 2x"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/icon_plugin.js:0
msgid "Icon size 3x"
msgstr "Mida d'icona 3x"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/icon_plugin.js:0
msgid "Icon size 4x"
msgstr "Mida d'icona 4x"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/icon_plugin.js:0
msgid "Icon size 5x"
msgstr "Mida d'icona 5x"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/media_dialog.js:0
msgid "Icons"
msgstr "Icones"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.xml:0
msgid "Illustrations"
msgstr "Il·lustracions"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_plugin.js:0
msgid "Image"
msgstr "Imatge"

#. module: html_editor
#: model:ir.model.fields,field_description:html_editor.field_ir_attachment__image_height
msgid "Image Height"
msgstr "Alçada de la imatge"

#. module: html_editor
#: model:ir.model.fields,field_description:html_editor.field_ir_attachment__image_src
msgid "Image Src"
msgstr "Font de la imatge"

#. module: html_editor
#: model:ir.model.fields,field_description:html_editor.field_ir_attachment__image_width
msgid "Image Width"
msgstr "Amplada de la imatge"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Image padding"
msgstr "Espaiat de la imatge"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/media_dialog.js:0
msgid "Images"
msgstr "Imatges"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_alternatives_dialog.xml:0
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_prompt_dialog.xml:0
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_translate_dialog.xml:0
msgid "Insert"
msgstr "Insereix"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/embedded_components/plugins/video_plugin/video_selector_dialog/video_selector_dialog.xml:0
msgid "Insert Video"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/embedded_components/plugins/video_plugin/video_plugin.js:0
msgid "Insert a Video"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/banner_plugin.js:0
msgid "Insert a danger banner"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/dynamic_placeholder_plugin.js:0
msgid "Insert a field"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/core/dom_plugin.js:0
msgid "Insert a horizontal rule separator"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/star_plugin.js:0
msgid "Insert a rating"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/star_plugin.js:0
msgid "Insert a rating over 3 stars"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/star_plugin.js:0
msgid "Insert a rating over 5 stars"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/banner_plugin.js:0
msgid "Insert a success banner"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/table/table_ui_plugin.js:0
msgid "Insert a table"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/banner_plugin.js:0
msgid "Insert a warning banner"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/table/table_menu.js:0
msgid "Insert above"
msgstr "Insereix a dalt"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/banner_plugin.js:0
msgid "Insert an info banner"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/table/table_menu.js:0
msgid "Insert below"
msgstr "Insereix a sota"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_plugin.js:0
msgid "Insert image or icon"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/table/table_menu.js:0
msgid "Insert left"
msgstr "Insereix a l'esquerra"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/table/table_menu.js:0
msgid "Insert right"
msgstr "Insereix a la dreta"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/signature_plugin.js:0
msgid "Insert your signature"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "Label"
msgstr "Etiqueta"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.js:0
msgid "Large"
msgstr "Gran"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_alternatives_dialog.js:0
msgid "Lengthen"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/gradient_picker.xml:0
msgid "Linear"
msgstr "Lineal"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_plugin.js:0
#: code:addons/html_editor/static/src/main/link/link_popover.js:0
msgid "Link"
msgstr "Enllaç"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.js:0
msgid "Link copied to clipboard."
msgstr "Enllaç copiat al porta-retalls."

#. module: html_editor
#. odoo-python
#: code:addons/html_editor/controllers/main.py:0
msgid ""
"Link preview is not available because %s, please check if your url is "
"correct"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/list/list_plugin.js:0
msgid "List"
msgstr "Llista"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.xml:0
msgid "Load more..."
msgstr "Carregar més..."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_alternatives_dialog.xml:0
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_prompt_dialog.xml:0
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_translate_dialog.xml:0
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.xml:0
msgid "Loading..."
msgstr "Carregant..."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.js:0
msgid "Loop"
msgstr "Bucle"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/dynamic_placeholder_plugin.js:0
msgid "Marketing Tools"
msgstr "Eines de màrqueting"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_plugin.js:0
msgid "Media"
msgstr "Contingut multimèdia"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.js:0
msgid "Medium"
msgstr "Mitjà"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Medium section heading"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/embedded_components/core/file/readonly_file.js:0
msgid "Missing File"
msgstr "Falta el fitxer"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/powerbox/powerbox_plugin.js:0
msgid "More options"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/table/table_menu.js:0
msgid "Move down"
msgstr "Mou avall"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/table/table_menu.js:0
msgid "Move left"
msgstr "Mou a l'esquerra"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/table/table_menu.js:0
msgid "Move right"
msgstr "Mou a la dreta"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/table/table_menu.js:0
msgid "Move up"
msgstr "Mou amunt"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.xml:0
msgid "My Images"
msgstr "Les meves imatges"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_plugin.js:0
msgid "Navigation"
msgstr "Navegació"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.js:0
msgid "No URL specified"
msgstr "No s'ha especificat L'URL"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/document_selector.xml:0
msgid "No documents found."
msgstr "No s'han trobat documents."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/components/history_dialog/history_dialog.xml:0
msgid "No history"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/image_selector.xml:0
msgid "No images found."
msgstr "No s'han trobat imatges."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/icon_selector.xml:0
msgid "No pictograms found."
msgstr "No s'ha trobat cap pictograma."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Normal"
msgstr "Normal"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/list/list_plugin.js:0
msgid "Numbered list"
msgstr "Llista numerada"

#. module: html_editor
#. odoo-python
#: code:addons/html_editor/controllers/main.py:0
msgid "Oops, it looks like our AI is unreachable!"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/embedded_components/core/file/readonly_file.js:0
msgid ""
"Oops, the file %s could not be found. Please replace this file box by a new "
"one to re-upload the file."
msgstr ""
"No s'ha pogut trobar el fitxer %s. Substituïu aquest quadre de fitxers per "
"un de nou per tornar a pujar el fitxer."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "Open in a new tab"
msgstr "Obrir en una pestanya nova"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/image_selector.xml:0
msgid "Optimized"
msgstr "Optimitzat"

#. module: html_editor
#: model:ir.model.fields,field_description:html_editor.field_ir_attachment__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "Adjunció original (sense optimitzar ni redimensionar)"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.js:0
msgid "Outline"
msgstr "Contorn"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.js:0
msgid "Outline + Rounded"
msgstr "Contornejar + Arrodoniment"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Paragraph"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Paragraph block"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_plugin.js:0
msgid "Paste as URL"
msgstr "Pegar com a URL"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_alternatives_dialog.js:0
msgid "Persuasive"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/gradient_picker.xml:0
msgid "Position X"
msgstr "Posició X"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/gradient_picker.xml:0
msgid "Position Y"
msgstr "Posició Y"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.xml:0
msgid "Preview"
msgstr "Vista prèvia"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Preview image"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_alternatives_dialog.js:0
msgid "Professional"
msgstr "Professional"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/upload_progress_toast/upload_progress_toast.xml:0
msgid "Progress bar"
msgstr "Barra de progrès"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Quote"
msgstr "Cita"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/gradient_picker.xml:0
msgid "Radial"
msgstr "Radial"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Remove (DELETE)"
msgstr "Remoure (ELIMINAR)"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/core/format_plugin.js:0
msgid "Remove Format"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_plugin.js:0
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "Remove Link"
msgstr "Remoure enllaç"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/column_plugin.js:0
msgid "Remove columns"
msgstr "Remoure columnes"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "Replace URL with its title?"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_plugin.js:0
msgid "Replace media"
msgstr "Reemplaça el contingut  multimèdia"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/color_selector.xml:0
msgid "Reset"
msgstr "Reinicia"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_crop.xml:0
msgid "Reset Image"
msgstr "Reiniciar imatge"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/table/table_menu.js:0
msgid "Reset Size"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Resize Default"
msgstr "Redimensionar a mida predeterminada"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Resize Full"
msgstr "Redimensionar a mida completa"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Resize Half"
msgstr "Redimensionar a mida mitjana"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Resize Quarter"
msgstr "Redimensionar a un quart de la mida"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/components/history_dialog/history_dialog.xml:0
msgid "Restore history"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_crop.xml:0
msgid "Rotate Left"
msgstr "Rotar a l'esquerra"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_crop.xml:0
msgid "Rotate Right"
msgstr "Rotar a la dreta"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_description.xml:0
msgid "Save"
msgstr "Desar"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/document_selector.js:0
msgid "Search a document"
msgstr "Cerca un document"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/icon_selector.xml:0
msgid "Search a pictogram"
msgstr "Cerca un pictograma"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/image_selector.js:0
msgid "Search an image"
msgstr "Cerca una imatge"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/gradient_picker.xml:0
msgid "Second color %"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/media_dialog.js:0
msgid "Select a media"
msgstr "Selecciona un contingut multimèdia"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_prompt_dialog.xml:0
msgid "Send a message"
msgstr "Enviar un missatge"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/core/dom_plugin.js:0
msgid "Separator"
msgstr "Separador"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Shape: Circle"
msgstr "Forma: Cercle"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Shape: Rounded"
msgstr "Forma: Arrodonit"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Shape: Shadow"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Shape: Thumbnail"
msgstr "Forma: Miniatura"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_alternatives_dialog.js:0
msgid "Shorten"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.xml:0
msgid "Show optimized images"
msgstr "Mostrar imatges optimitzades"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/signature_plugin.js:0
msgid "Signature"
msgstr "Signatura"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/gradient_picker.xml:0
msgid "Size"
msgstr "Mida"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.js:0
msgid "Small"
msgstr "Petit"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Small section heading"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/color_selector.xml:0
msgid "Solid"
msgstr "Sòlid"

#. module: html_editor
#. odoo-python
#: code:addons/html_editor/controllers/main.py:0
msgid "Sorry, we could not generate a response. Please try again later."
msgstr ""

#. module: html_editor
#. odoo-python
#: code:addons/html_editor/controllers/main.py:0
msgid "Sorry, your prompt is too long. Try to say it in fewer words."
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/star_plugin.js:0
msgid "Stars"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/powerbox/powerbox_plugin.js:0
msgid "Structure"
msgstr "Estructura"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.xml:0
msgid "Suggestions"
msgstr "Suggeriments"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/text_direction_plugin.js:0
msgid "Switch direction"
msgstr "Canviar de direcció"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/text_direction_plugin.js:0
msgid "Switch the text's direction"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/table/table_ui_plugin.js:0
msgid "Table"
msgstr "Taula"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/embedded_components/plugins/table_of_content_plugin/table_of_content_plugin.js:0
msgid "Table Of Content"
msgstr "Taula de continguts"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/font_plugin.js:0
msgid "Text"
msgstr "Text"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.xml:0
msgid "The URL does not seem to work."
msgstr "Sembla que l'URL no funciona."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.xml:0
msgid "The URL seems valid."
msgstr "L'URL sembla vàlida."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.js:0
msgid "The provided url does not reference any supported video"
msgstr "L'URL proporcionat no fa referència a cap vídeo compatible"

#. module: html_editor
#. odoo-python
#: code:addons/html_editor/tools.py:0
msgid "The provided url is invalid"
msgstr "L'URL proporcionada no és vàlida"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.js:0
msgid "The provided url is not valid"
msgstr "L'URL proporcionada no és vàlida"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_prompt_dialog.xml:0
msgid "Thinking..."
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.js:0
msgid "This URL is invalid. Preview couldn't be updated."
msgstr "Aquest URL no és vàlid. La vista prèvia no s'ha pogut actualitzar."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.js:0
msgid "This file is a public view attachment."
msgstr "L'arxiu és un arxiu adjunt de vista pública."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.js:0
msgid "This file is attached to the current record."
msgstr "L'arxiu està adjunt al registre actual."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_crop.js:0
msgid "This image is an external image"
msgstr "Aquesta imatge és una imatge externa."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_crop.js:0
msgid ""
"This type of image is not supported for cropping.<br/>If you want to crop "
"it, please first download it from the original source and upload it in Odoo."
msgstr ""
"No es pot retallar aquest tipus d'imatge.<br/>Si vols retallar-la, "
"descarrega-la de la font d'origen i carrega-la a Odoo."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/core/format_plugin.js:0
msgid "Toggle bold"
msgstr "Alternar negretes"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/icon_plugin.js:0
msgid "Toggle icon spin"
msgstr "Alternar gir d'icona"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/core/format_plugin.js:0
msgid "Toggle italic"
msgstr "Alternar cursiva"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/core/format_plugin.js:0
msgid "Toggle strikethrough"
msgstr "Alternar ratllat"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/core/format_plugin.js:0
msgid "Toggle underline"
msgstr "Alternar subratllat"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_description.xml:0
msgid "Tooltip"
msgstr "Informació sobre eines"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/list/list_plugin.js:0
msgid "Track tasks with a checklist"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_plugin.js:0
msgid "Transform the picture (click twice to reset transformation)"
msgstr ""
"Transforma la imatge (fes clic dos cops per reiniciar la transformació)"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/language_selector.xml:0
msgid "Translate"
msgstr "Traduir "

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_plugin.js:0
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_translate_dialog.xml:0
msgid "Translate with AI"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_translate_dialog.xml:0
msgid "Translating..."
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/icon_selector.xml:0
msgid "Try searching with other keywords."
msgstr "Proveu de cercar amb altres paraules clau."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/gradient_picker.xml:0
msgid "Type"
msgstr "Tipus"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/powerbox/powerbox_plugin.js:0
msgid "Type \"/\" for commands"
msgstr "Escrigui \"/\" per a accedir a les comandes"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "Type your URL"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "Type your link label"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "URL"
msgstr "L'URL"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "Upload File"
msgstr "Pujar fitxers"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/document_selector.js:0
msgid "Upload a document"
msgstr "Carregar un document"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/file_plugin.js:0
#: code:addons/html_editor/static/src/others/embedded_components/plugins/file_plugin/file_plugin.js:0
msgid "Upload a file"
msgstr "Puja un fitxer"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/image_selector.js:0
msgid "Upload an image"
msgstr "Carregar una imatge"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/image_selector.js:0
msgid "Uploaded image's format is not supported. Try with: "
msgstr "El format de la imatge carregada no és compatible. Provi amb:"

#. module: html_editor
#. odoo-python
#: code:addons/html_editor/controllers/main.py:0
msgid "Uploaded image's format is not supported. Try with: %s"
msgstr "El format de la imatge carregada no és compatible. Provi amb: %s"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/embedded_components/plugins/video_plugin/video_plugin.js:0
msgid "Video Link"
msgstr "Enllaç de vídeo"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.xml:0
msgid "Video code"
msgstr "Codi de vídeo"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/embedded_components/core/video/video.xml:0
msgid "Video player"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/media_dialog.js:0
msgid "Videos"
msgstr "Vídeos"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.js:0
msgid "Videos are muted when autoplay is enabled"
msgstr ""
"Els vídeos estan silenciats quan la reproducció automàtica està habilitada"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.xml:0
msgid "Vimeo"
msgstr "Vimeo"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/powerbox/powerbox_plugin.js:0
msgid "Widget"
msgstr "Widget"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/image_selector.xml:0
msgid ""
"Wow, it feels a bit empty in here. Upload from the button in the top right "
"corner!"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "Yes"
msgstr "Sí"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/document_selector.xml:0
msgid ""
"You can upload documents with the button located in the top left of the "
"screen."
msgstr ""
"Pots pujar documents amb el botó localitzat a dalt a l'esquerra de la "
"pantalla."

#. module: html_editor
#. odoo-python
#: code:addons/html_editor/controllers/main.py:0
msgid ""
"You have reached the maximum number of requests for this service. Try again "
"later."
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/dynamic_placeholder_plugin.js:0
msgid ""
"You need to select a model before opening the dynamic placeholder selector."
msgstr ""

#. module: html_editor
#. odoo-python
#: code:addons/html_editor/controllers/main.py:0
msgid "You need to specify either data or url to create an attachment."
msgstr "Heu d'especificar la data o l'URL per crear un arxiu adjunt."

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.xml:0
msgid "Youku"
msgstr "Youku"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/others/embedded_components/core/video/video.xml:0
msgid "Your browser does not support iframe."
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_dialog.js:0
msgid "Your content was successfully generated."
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.xml:0
msgid "Youtube"
msgstr "Youtube"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_crop.xml:0
msgid "Zoom In"
msgstr "Apropa"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/image_crop.xml:0
msgid "Zoom Out"
msgstr "Allunya"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.xml:0
msgid "all"
msgstr "tots"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/chatgpt/chatgpt_alternatives_dialog.xml:0
msgid "alternatives..."
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.xml:0
msgid "and"
msgstr "i"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.xml:0
msgid "database"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/font/gradient_picker.xml:0
msgid "deg"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/file_plugin.js:0
msgid "document"
msgstr "document"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/file_plugin.js:0
msgid "file"
msgstr "fitxer"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/file_selector.xml:0
msgid "media-library"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "or"
msgstr "o"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/media/media_dialog/video_selector.xml:0
msgid "videos"
msgstr "vídeos"

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "{{state.urlDescription}}"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "{{state.urlTitle}}"
msgstr ""

#. module: html_editor
#. odoo-javascript
#: code:addons/html_editor/static/src/main/link/link_popover.xml:0
msgid "{{state.url}}"
msgstr ""
