# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_account
# 
# Translators:
# Sarah <PERSON>, 2025
# Wil <PERSON>doo, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:04+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/plugins/accounting_plugin.js:0
msgid "%s is not a valid year."
msgstr "%s년은 유효하지 않습니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"'%s' is not a valid period. Supported formats are \"21/12/2022\", "
"\"Q1/2022\", \"12/2022\", and \"2022\"."
msgstr ""
"'%s' 는 유효한 기간이 아닙니다. \"21/12/2022\", \"Q1/2022\", \"12/2022\", 및 \"2022\" 과 "
"같은 형식만 지원됩니다."

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_account_account
msgid "Account"
msgstr "계정"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Bank and Cash"
msgstr "은행 및 현금"

#. module: spreadsheet_account
#. odoo-python
#: code:addons/spreadsheet_account/models/account.py:0
msgid "Cell Audit"
msgstr "셀 감사"

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_res_company
msgid "Companies"
msgstr "회사"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Cost of Revenue"
msgstr "매출원가"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Credit Card"
msgstr "신용카드"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Current Assets"
msgstr "유동자산"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Current Liabilities"
msgstr "유동부채"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Current Year Earnings"
msgstr "당기순이익"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Depreciation"
msgstr "감가상각"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Equity"
msgstr "자기 자본"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Expenses"
msgstr "경비"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Fixed Assets"
msgstr "고정자산"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total balance for the specified account(s) and period."
msgstr "지정된 계정 및 기간의 총 잔액을 가져옵니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total credit for the specified account(s) and period."
msgstr "지정된 계정 및 기간에 대한 총 대변을 가져옵니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Get the total debit for the specified account(s) and period."
msgstr "지정된 계정 및 기간에 대한 총 차변을 가져옵니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Income"
msgstr "수익"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Non-current Assets"
msgstr "고정자산"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Non-current Liabilities"
msgstr "비유동부채"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Off-Balance Sheet"
msgstr "부외항목"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Offset applied to the years."
msgstr "연도에 적용된 오프셋."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Other Income"
msgstr "기타 소득"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Payable"
msgstr "지급금"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Prepayments"
msgstr "선급금"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/account_group_auto_complete.js:0
msgid "Receivable"
msgstr "미수금"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Return the partner balance for the specified account(s) and period"
msgstr "지정된 계정과 기간에 대한 파트너 잔액을 반환합니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Return the residual amount for the specified account(s) and period"
msgstr "지정된 계정과 기간에 대한 잔액을 반환합니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Returns the account codes of a given group."
msgstr "지정된 그룹의 계정 코드를 반환합니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"Returns the ending date of the fiscal year encompassing the provided date."
msgstr "제공된 날짜를 포함하여 회계 연도의 종료일을 반환합니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"Returns the starting date of the fiscal year encompassing the provided date."
msgstr "제공된 날짜를 포함하여 회계 연도의 시작일을 반환합니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/index.js:0
msgid "See records"
msgstr "레코드 보기"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "Set to TRUE to include unposted entries."
msgstr "게시되지 않은 항목을 포함하려면 TRUE로 설정하세요."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/plugins/accounting_plugin.js:0
msgid "The balance for given partners could not be computed."
msgstr "지정된 파트너의 잔액을 계산할 수 없습니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/plugins/accounting_plugin.js:0
msgid "The company fiscal year could not be found."
msgstr "회사의 회계 연도를 찾을 수 없습니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The company to target (Advanced)."
msgstr "타겟팅할 회사 (고급)."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The company."
msgstr "회사."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"The date range. Supported formats are \"21/12/2022\", \"Q1/2022\", "
"\"12/2022\", and \"2022\"."
msgstr "날짜의 범위입니다. \"21/12/2022\", \"Q1/2022\", \"12/2022\" 및 \"2022\"과 같은 형식이 지원됩니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The day from which to extract the fiscal year end."
msgstr "회계연도 종료일을 추출하는 날짜입니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The day from which to extract the fiscal year start."
msgstr "회계연도 시작일을 추출하는 날짜입니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The partner ids (separated by a comma)."
msgstr "파트너 ID (쉼표로 구분)."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The prefix of the accounts."
msgstr "계정 접두사."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid ""
"The prefix of the accounts. If none provided, all receivable and payable "
"accounts will be used."
msgstr "계정의 접두사를 설정하세요. 입력하지 않으면 모든 미수금 및 미지급금 계정이 사용됩니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/plugins/accounting_plugin.js:0
msgid "The residual amount for given accounts could not be computed."
msgstr "지정된 계정의 잔액을 계산할 수 없습니다."

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
msgid "The technical account type (possible values are: %s)."
msgstr "기술 계정 유형 (가능한 값: %s)."
