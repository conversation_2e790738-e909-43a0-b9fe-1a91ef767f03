# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_sale
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:48+0000\n"
"PO-Revision-Date: 2019-08-26 09:13+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"Language: lb\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
msgid "(left:"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/ProductScreen/Orderline.xml:0
msgid "(tax incl.)"
msgstr ""

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.report_invoice_document
msgid "<span style=\"margin: 0px 5px;\">:</span>"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "A new order has been created."
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "Apply a down payment (fixed amount)"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "Apply a down payment (percentage)"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
msgid "Back"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/MobileSaleOrderManagementScreen.xml:0
msgid "Back to list"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
msgid "Cancelled"
msgstr ""

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
msgid "Customer"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "Customer loading error"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
msgid "Date"
msgstr ""

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.message_body
msgid "Delivered from"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "Do you want to load the SN/Lots linked to the Sales Order?"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "Down Payment"
msgstr ""

#. module: pos_sale
#: model:product.template,name:pos_sale.default_downpayment_product_product_template
msgid "Down Payment (POS)"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__down_payment_details
msgid "Down Payment Details"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_config__down_payment_product_id
#: model:ir.model.fields,field_description:pos_sale.field_res_config_settings__pos_down_payment_product_id
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "Down Payment Product"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "Due balance: %s"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
msgid "E.g. customer: Steward, date: 2020-05-09"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/ReceiptScreen/OrderReceipt.xml:0
#: model_terms:ir.ui.view,arch_db:pos_sale.report_invoice_document
msgid "From"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__invoiced
msgid "Invoiced"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid ""
"It seems that you didn't configure a down payment product in your point of sale.\n"
"                        You can go to your point of sale configuration to choose one."
msgstr ""

#. module: pos_sale
#. odoo-python
#: code:addons/pos_sale/models/sale_order.py:0
msgid "Linked POS Orders"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__sale_order_origin_id
msgid "Linked Sale Order"
msgstr ""

#. module: pos_sale
#. odoo-python
#: code:addons/pos_sale/models/pos_order.py:0
msgid "Linked Sale Orders"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
msgid "Locked"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__pos_draft
msgid "New"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
msgid "Next Order List"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "No"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "No down payment product"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_sessions_open_count
msgid "Open POS Sessions"
msgstr ""

#. module: pos_sale
#: model:ir.actions.act_window,name:pos_sale.pos_session_action_from_crm_team
msgid "Open Sessions"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
msgid "Order"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__pos_order_line_ids
#: model:ir.model.fields,field_description:pos_sale.field_sale_order_line__pos_order_line_ids
msgid "Order lines Transfered to Point of Sale"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__paid
msgid "Paid"
msgstr ""

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_config
msgid "Point of Sale Configuration"
msgstr ""

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr ""

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_order
msgid "Point of Sale Orders"
msgstr ""

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_session
msgid "Point of Sale Session"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_config_ids
msgid "Point of Sales"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__pos_order_count
msgid "Pos Order Count"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__pos_done
msgid "Posted"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
msgid "Previous Order List"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "Products not available in POS"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
msgid "Quotation"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
msgid "Quotation Sent"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/SetSaleOrderButton.xml:0
msgid "Quotation/Order"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "SN/Lots Loading"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/ProductScreen/Orderline.xml:0
msgid "SO"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__sale_order_count
msgid "Sale Order Count"
msgstr ""

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "Sales"
msgstr ""

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#: model:ir.model,name:pos_sale.model_sale_order
msgid "Sales Order"
msgstr ""

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr ""

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_crm_team
#: model:ir.model.fields,field_description:pos_sale.field_pos_config__crm_team_id
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__crm_team_id
#: model:ir.model.fields,field_description:pos_sale.field_pos_session__crm_team_id
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "Sales Team"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_res_config_settings__pos_crm_team_id
msgid "Sales Team (PoS)"
msgstr ""

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "Sales are reported to the following sales team"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
msgid "Salesman"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
msgid "Salesperson"
msgstr ""

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_view_kanban_dashboard
msgid "Session Running"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_order_amount_total
msgid "Session Sale Amount"
msgstr ""

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_view_kanban_dashboard
msgid "Sessions Running"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/SetSaleOrderButton.xml:0
msgid "Set Sale Order"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "Settle the order"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "Some of the products in your Sale Order are not available in POS, do you want to import them?"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__sale_order_line_id
msgid "Source Sale Order Line"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
msgid "State"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_report__state
msgid "Status"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_sale_order__amount_unpaid
msgid "The amount due from the sale order."
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_order__currency_rate
msgid "The rate of the currency to the currency of rate applicable at the date of the order"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "There was a problem in loading the %s customer."
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_config__crm_team_id
#: model:ir.model.fields,help:pos_sale.field_pos_session__crm_team_id
#: model:ir.model.fields,help:pos_sale.field_res_config_settings__pos_crm_team_id
msgid "This Point of sale's sales will be related to this Sales Team."
msgstr ""

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "This product will be applied when down payment is made"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_config__down_payment_product_id
#: model:ir.model.fields,help:pos_sale.field_res_config_settings__pos_down_payment_product_id
msgid "This product will be used as down payment on a sale order."
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
msgid "Total"
msgstr ""

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.view_pos_order_form_inherit_pos_sale
msgid ""
"Transfered<br/>\n"
"                                from Sale"
msgstr ""

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.view_order_form_inherit_pos_sale
msgid ""
"Transfered<br/>\n"
"                                to POS"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__amount_unpaid
msgid "Unpaid Amount"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "What do you want to do?"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "Yes"
msgstr ""

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
msgid "You have tried to charge a down payment of %s but only %s remains to be paid, %s will be applied to the purchase order line."
msgstr ""
