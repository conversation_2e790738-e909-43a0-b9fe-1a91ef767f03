# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_survey
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_recruitment_survey
#: model:mail.template,body_html:hr_recruitment_survey.mail_template_applicant_interview_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'applicant'\">[applicant name]</t><br/><br/>\n"
"        <t>\n"
"            You've progressed through the recruitment process and we would like you to answer some questions.\n"
"        </t>\n"
"        </p><div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t>\n"
"                    Start the written interview\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Please answer the interview for <t t-out=\"format_date(object.deadline)\">[deadline date]</t>.<br/><br/>\n"
"        </t>\n"
"        <t>\n"
"            We wish you good luck! Thank you in advance for your participation.\n"
"        </t>\n"
"    \n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Cher/Chère <t t-out=\"object.partner_id.name or 'applicant'\">[nom du candidat]</t><br/><br/>\n"
"        <t>\n"
"            Vous avez progressé dans le processus de recrutement et nous aimerions que vous répondiez à quelques questions.\n"
"        </t>\n"
"        </p><div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t>\n"
"                    Commencer l'entretien écrit\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Pourriez-vous répondre à l'entretien avant le <t t-out=\"format_date(object.deadline)\">[date d'échéance]</t>.<br/><br/>\n"
"        </t>\n"
"        <t>\n"
"            Bonne chance ! Merci d'avance pour votre participation.\n"
"        </t>\n"
"    \n"
"</div>\n"
"            "

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_recruitment_survey_button_form_view
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Go to Recruitment"
msgstr "<i class=\"oi oi-fw oi-arrow-right\"/>Aller vers Recrutement"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid ""
"<span class=\"o_stat_text\">Consult</span>\n"
"                        <span class=\"o_stat_text\">Interview</span>"
msgstr ""
"<span class=\"o_stat_text\">Consulter</span>\n"
"                        <span class=\"o_stat_text\">Entretien</span>"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1
msgid "About you"
msgstr "À propos de vous"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q7
msgid "Activities"
msgstr "Activités"

#. module: hr_recruitment_survey
#: model_terms:ir.actions.act_window,help:hr_recruitment_survey.survey_survey_action_recruitment
msgid "Add a new survey"
msgstr "Ajouter un nouveau sondage"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_invite__applicant_id
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_user_input__applicant_id
msgid "Applicant"
msgstr "Candidat"

#. module: hr_recruitment_survey
#: model:mail.template,name:hr_recruitment_survey.mail_template_applicant_interview_invite
msgid "Applicant: Interview"
msgstr "Candidat : Entretien"

#. module: hr_recruitment_survey
#: model:ir.model.fields,help:hr_recruitment_survey.field_hr_applicant__survey_id
#: model:ir.model.fields,help:hr_recruitment_survey.field_hr_job__survey_id
msgid ""
"Choose an interview form for this job position and you will be able to "
"print/answer this interview from all applicants who apply for this job"
msgstr ""
"Sélectionnez un formulaire d'entretien pour ce poste et vous pourrez "
"l'imprimer et y répondre pour chaque candidat ayant postulé ce poste."

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Display Interview Form"
msgstr "Afficher le formulaire d'entretien"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q4
msgid "Education"
msgstr "Éducation"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q2
msgid "From which university did or will you graduate?"
msgstr ""
"De quelle université avez-vous obtenu ou allez-vous obtenir votre diplôme ?"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row2
msgid "Getting on with colleagues"
msgstr "S'entendre avec ses collègues"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row8
msgid "Getting perks such as free parking, gym passes"
msgstr ""
"Obtenir des avantages tels qu'un parking gratuit, des abonnements à une "
"salle de fitness"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row1
msgid "Having a good pay"
msgstr "Avoir un bon salaire"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row3
msgid "Having a nice office environment"
msgstr "Avoir un bel environnement de travail"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row7
msgid "Having freebies such as tea, coffee and stationery"
msgstr "Avoir des bonus tels que du thé, café et fournitures"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col2
msgid "Important"
msgstr "Important"

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_job__survey_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Interview Form"
msgstr "Formulaire d'entretien"

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/hr_job.py:0
msgid "Interview Form: %s"
msgstr "Formulaire d'entretien : %s"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.res_config_settings_view_form
msgid "Interview Survey"
msgstr "Questionnaire d'entretien"

#. module: hr_recruitment_survey
#: model:ir.actions.act_window,name:hr_recruitment_survey.survey_survey_action_recruitment
#: model:ir.ui.menu,name:hr_recruitment_survey.menu_hr_recruitment_config_surveys
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Interviews"
msgstr "Entretiens"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_hr_job
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_survey__hr_job_ids
msgid "Job Position"
msgstr "Poste"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q6
msgid "Knowledge"
msgstr "Connaissances"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row6
msgid "Management quality"
msgstr "Qualité de la gestion"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col1
msgid "Not important"
msgstr "Pas important"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row5
msgid "Office location"
msgstr "Emplacement des bureaux"

#. module: hr_recruitment_survey
#: model:mail.template,subject:hr_recruitment_survey.mail_template_applicant_interview_invite
msgid "Participate to {{ object.survey_id.display_name }} interview"
msgstr "Participer à {{ object.survey_id.display_name }} entretien"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q5
msgid "Past work experiences"
msgstr "Expériences professionnelles passées"

#. module: hr_recruitment_survey
#: model_terms:survey.survey,description:hr_recruitment_survey.survey_recruitment_form
msgid ""
"Please answer those questions to help recruitment officers to preprocess "
"your application."
msgstr ""
"Veuillez répondre à ces questions pour aider les recruteurs à prétraiter "
"votre candidature."

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1
msgid ""
"Please fill information about you: who you are, what are your education, experience, and activities.\n"
"    It will help us managing your application."
msgstr ""
"Veuillez compléter les informations vous concernant : qui vous êtes, quelle est votre formation, votre expérience et vos activités. \n"
"Cela nous aidera à gérer votre candidature."

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/hr_applicant.py:0
msgid "Please provide an applicant name."
msgstr "Veuillez indiquer le nom du candidat."

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q5
msgid ""
"Please summarize your education history: schools, location, diplomas, ..."
msgstr ""
"Veuillez résumer votre parcours scolaire : écoles, lieux, diplômes,..."

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q7
msgid ""
"Please tell us a bit more about yourself: what are your main activities, ..."
msgstr ""
"Veuillez nous en dire un peu plus sur vous : quelles sont vos principales "
"activités,..."

#. module: hr_recruitment_survey
#: model:ir.model.fields.selection,name:hr_recruitment_survey.selection__survey_survey__survey_type__recruitment
msgid "Recruitment"
msgstr "Recrutement"

#. module: hr_recruitment_survey
#: model:survey.survey,title:hr_recruitment_survey.survey_recruitment_form
msgid "Recruitment Form"
msgstr "Formulaire de recrutement"

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant__response_ids
msgid "Responses"
msgstr "Réponses"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid "See interview report"
msgstr "Voir le rapport des entretiens"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid "Send Interview"
msgstr "Envoyer l'entretien"

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/hr_applicant.py:0
msgid "Send an interview"
msgstr "Envoyer un entretien"

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/hr_job.py:0
#: model:ir.model,name:hr_recruitment_survey.model_survey_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant__survey_id
msgid "Survey"
msgstr "Sondage"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr "Assistant d'invitation au sondage"

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_survey__survey_type
msgid "Survey Type"
msgstr "Type de sondage"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Entrée utilisateur du sondage"

#. module: hr_recruitment_survey
#: model_terms:survey.survey,description_done:hr_recruitment_survey.survey_recruitment_form
msgid "Thank you for answering this survey. We will come back to you soon."
msgstr ""
"Merci d'avoir répondu à ce sondage. Nous reviendrons vers vous "
"prochainement."

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/survey_user_input.py:0
msgid "The applicant \"%s\" has finished the survey."
msgstr "Le candidat \"%s\" a terminé le questionnaire."

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/wizard/survey_invite.py:0
msgid "The survey %(survey_link)s has been sent to %(partner_link)s"
msgstr "Le questionnaire %(survey_link)s a été envoyé à %(partner_link)s"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col3
msgid "Very important"
msgstr "Très important"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q3
msgid "Were you referred by an employee?"
msgstr "Avez-vous été référencé par un employé ? "

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q6
msgid "What are your main knowledge regarding the job you are applying to?"
msgstr ""
"Quelles sont vos principales connaissances concernant le poste que vous "
"postulez ?"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "What is important for you?"
msgstr "Qu'est-ce qui est important pour vous ?"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q1
msgid "Which country are you from?"
msgstr "De quel pays venez-vous ?"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row4
msgid "Working with state of the art technology"
msgstr "Travailler avec une technologie de pointe"

#. module: hr_recruitment_survey
#: model_terms:ir.actions.act_window,help:hr_recruitment_survey.survey_survey_action_recruitment
msgid ""
"You can create surveys used for recruitments. Design easily your interview,\n"
"                send invitations and analyze answers."
msgstr ""
"Vous pouvez créer des sondages utilisés pour les recrutements. Concevez facilement votre entretien,\n"
"                envoyez des invitations et analysez les réponses."
