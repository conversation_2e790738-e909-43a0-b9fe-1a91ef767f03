# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_recaptcha
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.2alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-12 10:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: google_recaptcha
#: model_terms:ir.ui.view,arch_db:google_recaptcha.res_config_settings_view_form
msgid "<i class=\"oi oi-arrow-right\"/> Generate reCAPTCHA v3 keys"
msgstr "<i class=\"oi oi-arrow-right\"/> Generar las claves reCAPTCHA v3 "

#. module: google_recaptcha
#: model:ir.model.fields,help:google_recaptcha.field_res_config_settings__recaptcha_min_score
msgid ""
"By default, should be one of 0.1, 0.3, 0.7, 0.9.\n"
"1.0 is very likely a good interaction, 0.0 is very likely a bot"
msgstr ""
"Por defecto debería ser 0.1, 0.3, 0.7, 0.9. \n"
"1.0 muy probablemente es una buena interacción, 0.0 muy probablemente es un bot."

#. module: google_recaptcha
#: model:ir.model,name:google_recaptcha.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: google_recaptcha
#: model:ir.model,name:google_recaptcha.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutamiento HTTP "

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_min_score
msgid "Minimum score"
msgstr "Puntuación mínima"

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/js/recaptcha.js:0
msgid "No recaptcha site key set."
msgstr "No se estableció ninguna clave de sitio de reCAPTCHA."

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "Privacy Policy"
msgstr "Política de privacidad"

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "Protected by reCAPTCHA,"
msgstr "Protegido por reCAPTCHA,"

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_private_key
msgid "Secret Key"
msgstr "Clave secreta"

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_public_key
msgid "Site Key"
msgstr "Clave del sitio"

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "Terms of Service"
msgstr "Términos de Servicio"

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "The reCaptcha private key is invalid."
msgstr "La clave privada de reCAPTCHA no es válida."

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "The reCaptcha token is invalid."
msgstr "El token de reCAPTCHA no es válido."

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/js/recaptcha.js:0
msgid "The recaptcha site key is invalid."
msgstr "La clave de sitio de reCAPTCHA no es válida."

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "The request is invalid or malformed."
msgstr "La solicitud no es válida o tiene un formato incorrecto."

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
msgid "Your request has timed out, please retry."
msgstr "Se agotó el tiempo de espera de su solicitud. Vuelva a intentarlo."

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
msgid "apply."
msgstr "se aplican."
