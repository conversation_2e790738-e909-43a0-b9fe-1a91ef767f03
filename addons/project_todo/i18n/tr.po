# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_todo
# 
# Translators:
# emre oktem, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Ed<PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"&amp;#128075; <br/>\n"
"    Welcome to the To-do app!"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a checklist\n"
"                    (/<span style=\"font-style: italic;\">checklist</span>)\n"
"                </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a separator\n"
"                    (/<span style=\"font-style: italic;\">separator</span>)\n"
"                </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Use\n"
"                    /<span style=\"font-style: italic;\">heading</span>\n"
"                    to convert a text into a title\n"
"                </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Below this list, try\n"
"            <span style=\"font-weight: bolder;\">commands</span>\n"
"            by\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font style=\"color: #017E84\">typing</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Select text to\n"
"            <font style=\"background-color: #017E84; color: white\">Highlight</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">strikethrough</span>\n"
"            or\n"
"            <span style=\"font-weight: bolder;\">style</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">it</span>\n"
"        </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Access your personal pipeline with your to-dos and assigned tasks by going to the Project app and clicking\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">My Tasks</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        There, your to-dos are listed as\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">private tasks.</font>\n"
"        </span>\n"
"        Any task you create privately will also be included in your to-dos. Essentially, they are interchangeable.\n"
"    </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        By default, to-dos are only visible to you. You can share them with other users by adding them as\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">assignees</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Customize the stages from the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Kanban view</font>\n"
"        </span>\n"
"        to reflect your preferred workflow.\n"
"    </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        If you want to assign your to-do to a specific project, open the ⚙️ menu and click\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">Convert to Task</font>.\n"
"        </span>\n"
"        This action will make it visible to other users.\n"
"    </span>\n"
"    <br/>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Use it to manage your work, take notes on the go, and create tasks based on them.\n"
"    </span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Wherever you are, use the magic keyboard shortcut to add yourself a reminder &amp;#128161;\n"
"    </span>\n"
"    <br/>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Check this box to indicate it's done</span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Click anywhere, and just start "
"typing</span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Press Ctrl+Z/⌘+Z to undo any change</span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">This private to-do is for you to play around with.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Ready to give it a spin?</span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "<span style=\"font-size: 14px;\">Try the following</span>"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Alt + Shift + T</font>\n"
"            </span>\n"
"            (Windows/Linux)"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Ctrl + Shift + T</font>\n"
"            </span>\n"
"            (MacOs)"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Add To-Do"
msgstr ""

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/web/activity/activity_menu_patch.js:0
msgid "Add a To-Do"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Add details about your to-do..."
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Archived"
msgstr "Arşivlendi"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__user_id
msgid "Assigned to"
msgstr "Atanan"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Assignees"
msgstr "Atananlar"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "By assigned tags"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "By personal stages"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Choose tags from the selected project"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Closed"
msgstr "Kapanmış"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Closed On"
msgstr "Kapanış"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/views/todo_form/todo_form_controller.js:0
#: model:ir.actions.act_window,name:project_todo.project_task_action_convert_todo_to_task
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Convert to Task"
msgstr "Görev Dönüştür"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Convert to-dos into tasks"
msgstr ""

#. module: project_todo
#: model:ir.model,name:project_todo.model_mail_activity_todo_create
msgid "Create activity and todo at the same time"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Create to-dos from anywhere"
msgstr ""

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_kanban
msgid "Delete"
msgstr "Sil"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Discard"
msgstr "Sil"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__date_deadline
msgid "Due Date"
msgstr "Vade Tarihi"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Future Activities"
msgstr "Sonraki Aktiviteler"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Group By"
msgstr "Grupla"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Hey"
msgstr ""

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__id
msgid "ID"
msgstr "ID"

#. module: project_todo
#: model_terms:ir.actions.act_window,help:project_todo.project_task_action_todo
msgid ""
"Keep your work organized by using memos and to-do lists.\n"
"                Your to-do items are private by default, but you can choose to share them with others by adding them as assignees."
msgstr ""

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Late Activities"
msgstr "Geciken Aktiviteler"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Manage your to-dos and assigned tasks from a single place"
msgstr ""

#. module: project_todo
#: model_terms:ir.actions.act_window,help:project_todo.project_task_action_todo
msgid "No to-do found. Let's create one!"
msgstr ""

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__note
msgid "Note"
msgstr "Not"

#. module: project_todo
#: model:res.groups,name:project_todo.group_onboarding_todo
msgid "Onboarding todo already generated for those users"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Open"
msgstr "Açık"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Organize your to-dos however you want"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Reminder to..."
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_kanban
msgid "Set Cover Image"
msgstr "Kapak Resmi Ayarla"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Show all records which has next action date is before today"
msgstr "Bir sonraki eylem tarihi bugünden önce olan tüm kayıtları göster"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_tree
msgid "Stage"
msgstr "Aşama"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Starred"
msgstr "Yıldızlı"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__summary
msgid "Summary"
msgstr "Özet"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Tags"
msgstr "Etiketler"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/res_users.py:0
#: model:ir.model,name:project_todo.model_project_task
msgid "Task"
msgstr "Görev"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/res_users.py:0
msgid "To-Do"
msgstr "Yapılacak"

#. module: project_todo
#: model:ir.ui.menu,name:project_todo.menu_todo_todos
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "To-do"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_quick_create_form
msgid "To-do Title"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "To-do..."
msgstr ""

#. module: project_todo
#: model:ir.actions.act_window,name:project_todo.project_task_action_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_activity
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_tree
msgid "To-dos"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Today Activities"
msgstr "Bugünkü Aktiviteler"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Todos"
msgstr ""

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/views/todo_form/todo_form_control_panel.xml:0
msgid "Toggle chatter"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "Type Here..."
msgstr ""

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/project_task.py:0
msgid "Untitled to-do"
msgstr ""

#. module: project_todo
#: model:ir.model,name:project_todo.model_res_users
msgid "User"
msgstr "Kullanıcı"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Using the editor"
msgstr ""

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/project_task.py:0
msgid "Welcome %s!"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Who has access to what?"
msgstr ""

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/wizard/mail_activity_todo_create.py:0
msgid "Your to-do has been successfully added to your pipeline."
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "convert-todo"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_quick_create_form
msgid "e.g. Send Invitations"
msgstr "Örneğin. Davetiye Gönder"

#. module: project_todo
#: model:ir.actions.server,name:project_todo.project_task_preload_action_todo
msgid "menu load To-dos"
msgstr ""

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "todo-access"
msgstr ""
