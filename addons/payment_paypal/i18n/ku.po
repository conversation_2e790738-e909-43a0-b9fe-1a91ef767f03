# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_paypal
# 
# Translators:
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Kurdish (https://app.transifex.com/odoo/teams/41243/ku/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ku\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "Client ID"
msgstr ""

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "Client Secret"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__code
msgid "Code"
msgstr "کۆد"

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_provider.py:0
msgid "Could not generate a new access token."
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_email_account
msgid "Email"
msgstr ""

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "Generate your webhook"
msgstr ""

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "How to configure your paypal account?"
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/controllers/main.py:0
msgid "Invalid response format, can't normalize."
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
msgid "Missing value for txn_id (%(txn_id)s) or txn_type (%(txn_type)s)."
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields.selection,name:payment_paypal.selection__payment_provider__code__paypal
msgid "PayPal"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_access_token
msgid "PayPal Access Token"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_access_token_expiry
msgid "PayPal Access Token Expiry"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_client_id
msgid "PayPal Client ID"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_client_secret
msgid "PayPal Client Secret"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_transaction__paypal_type
msgid "PayPal Transaction Type"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_webhook_id
msgid "PayPal Webhook ID"
msgstr ""

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_provider
msgid "Payment Provider"
msgstr ""

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_paypal
#. odoo-javascript
#: code:addons/payment_paypal/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
msgid "Received data with invalid payment status: %s"
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_provider.py:0
msgid "The communication with the API failed. Details: %s"
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
msgid "The customer left the payment page."
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__paypal_access_token_expiry
msgid "The moment at which the access token becomes invalid."
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__paypal_email_account
msgid ""
"The public business email solely used to identify the account with PayPal"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__paypal_access_token
msgid "The short-lived token used to access Paypal APIs"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "Webhook ID"
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_provider.py:0
msgid "You must have an HTTPS connection to generate a webhook."
msgstr ""
