{"manifest_version": 3, "name": "Discuss Push-to-talk", "description": "Empowers <PERSON><PERSON><PERSON> Discuss with system-wide Push-to-Talk", "version": "*******", "permissions": ["storage"], "action": {}, "background": {"service_worker": "background.js", "type": "module"}, "icons": {"128": "assets/icons/inactive_icon.png"}, "content_scripts": [{"matches": ["https://*/*", "http://*/*"], "js": ["content.js"], "run_at": "document_start"}], "commands": {"ptt-pressed": {"suggested_key": "Alt+Shift+Y", "description": "Push-to-Talk"}, "toggle-voice": {"suggested_key": "Alt+Shift+U", "description": "Voice Toggle"}}, "externally_connectable": {"matches": ["<all_urls>"]}}