# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_hr_expense
# 
# Translators:
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "# Expenses"
msgstr "# Despeses"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "Category"
msgstr "Categoria"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "Employee"
msgstr "Empleat"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "Expense"
msgstr "Despesa"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_hr_expense.spreadsheet_dashboard_expense
msgid "Expenses"
msgstr "Despeses"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "Expenses Analysis"
msgstr "Anàlisi de despeses"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "KPI - Expenses"
msgstr "KPI - Despeses"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "KPI - To reimburse"
msgstr "KPI - Per reemborsar"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "KPI - To report"
msgstr "KPI - Per informar"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "KPI - To validate"
msgstr "KPI - Per validar"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "Meals"
msgstr "Meals"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "Mileage"
msgstr "Kilometratge"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "Order"
msgstr "Ordre"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
msgid "Period"
msgstr "Període"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
msgid "Product"
msgstr "Producte"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "To reimburse"
msgstr "Per reemborsar"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "To report"
msgstr "Reportar"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "To validate"
msgstr "Per validar"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "Top Categories"
msgstr "Categories principals"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "Top Employees"
msgstr "Principals empleats"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "Top Expenses"
msgstr "Despeses superiors"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "Top Reinvoiced Orders"
msgstr "Top Comandes refacturades"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "Total"
msgstr "Total"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_sample_dashboard.json:0
msgid "Travel"
msgstr "Viatges"
