# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_purchase
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> Versada <<EMAIL>>, 2024\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: project_purchase
#: model:product.template,name:project_purchase.product_product_bricks_product_template
msgid "Bricks"
msgstr ""

#. module: project_purchase
#: model:product.template,name:project_purchase.product_product_cement_product_template
msgid "Cement"
msgstr ""

#. module: project_purchase
#. odoo-python
#: code:addons/project_purchase/models/project_project.py:0
msgid "No purchase order found. Let's create one."
msgstr ""

#. module: project_purchase
#. odoo-python
#: code:addons/project_purchase/models/project_project.py:0
msgid ""
"Once you ordered your products from your supplier, confirm your request for "
"quotation and it will turn into a purchase order."
msgstr ""

#. module: project_purchase
#: model:ir.model,name:project_purchase.model_project_project
#: model:ir.model.fields,field_description:project_purchase.field_purchase_order__project_id
msgid "Project"
msgstr "Projektas"

#. module: project_purchase
#: model:ir.model,name:project_purchase.model_purchase_order
msgid "Purchase Order"
msgstr "Pirkimo užsakymas"

#. module: project_purchase
#. odoo-python
#: code:addons/project_purchase/models/project_project.py:0
msgid "Purchase Order Items"
msgstr ""

#. module: project_purchase
#: model:ir.model,name:project_purchase.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Pirkimo užsakymo eilutė "

#. module: project_purchase
#. odoo-python
#: code:addons/project_purchase/models/project_project.py:0
#: model:ir.embedded.actions,name:project_purchase.project_embedded_action_purchase_orders
msgid "Purchase Orders"
msgstr "Pirkimų užsakymai"

#. module: project_purchase
#: model:product.template,name:project_purchase.product_product_sand_product_template
msgid "Sand"
msgstr ""
