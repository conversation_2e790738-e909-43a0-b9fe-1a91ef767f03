# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_media
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: social_media
#: model:ir.model,name:social_media.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_facebook
msgid "Facebook Account"
msgstr "Conta do Facebook"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_github
msgid "GitHub Account"
msgstr "Conta do GitHub"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_instagram
msgid "Instagram Account"
msgstr "Conta do Instagram"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_linkedin
msgid "LinkedIn Account"
msgstr "Conta do LinkedIn"

#. module: social_media
#: model_terms:ir.ui.view,arch_db:social_media.view_company_form_inherit_social_media
msgid "Social Media"
msgstr "Redes sociais"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_tiktok
msgid "TikTok Account"
msgstr "Conta do TikTok"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_twitter
msgid "X Account"
msgstr "Conta do X (Twitter)"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_youtube
msgid "Youtube Account"
msgstr "Conta do YouTube"
