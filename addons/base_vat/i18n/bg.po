# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_vat
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# KeyVillage, 2024
# <PERSON><PERSON><PERSON> <albena_viche<PERSON>@abv.bg>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:03+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"10XXXXXXXXY or 20XXXXXXXXY or 15XXXXXXXXY or 16XXXXXXXXY or 17XXXXXXXXY"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "17291716060 (NIN) or 1729171602 (VKN)"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "1792060346001 or 1792060346"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "3101012009"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "310175397400003 [Fifteen digits, first and last digits should be \"3\"]"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "49-098-576 or 49098576"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "AR200-5536168-2 or 20055361682"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "CHE-123.456.788 TVA or CHE-123.456.788 MWST or CHE-123.456.788 IVA"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "CO213123432-1 or CO213.123.432-1"
msgstr ""

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_company
msgid "Companies"
msgstr "Фирми"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_config_settings
msgid "Config Settings"
msgstr "Конфигурационни настройки"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"Connection with the VIES server failed. The VAT number %s could not be "
"validated."
msgstr ""

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "DE********8 or 12/345/67890"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "DO1-01-85004-3 or *********"
msgstr ""

#. module: base_vat
#: model:ir.model.fields,help:base_vat.field_res_partner__vies_valid
#: model:ir.model.fields,help:base_vat.field_res_users__vies_valid
msgid "European VAT numbers are automatically checked on the VIES database."
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"Example: '************' (format: 12 digits, all numbers, valid check digit)"
msgstr ""

#. module: base_vat
#: model:ir.model,name:base_vat.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Фискална позиция"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "GB********2 or XI********2"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "HU12345676 or ********-1-11 or **********"
msgstr ""

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"If this checkbox is ticked, the default fiscal position that applies will "
"depend upon the output of the verification by the European VIES Service."
msgstr ""

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__vies_valid
#: model:ir.model.fields,field_description:base_vat.field_res_users__vies_valid
msgid "Intra-Community Valid"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "MXGODE561231GR8 or GODE561231GR8"
msgstr ""

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__perform_vies_validation
#: model:ir.model.fields,field_description:base_vat.field_res_users__perform_vies_validation
msgid "Perform Vies Validation"
msgstr ""

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_base_vat_form
msgid "Tax ID"
msgstr "ДДС номер"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] for %(record_label)s does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "The VAT number %s could not be interpreted by the VIES server."
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
msgid ""
"The country detected for this foreign VAT number does not match any of the "
"countries composing the country group set on this fiscal position."
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
msgid ""
"The country of the foreign VAT number could not be detected. Please assign a"
" country to the fiscal position or set a country group"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid ""
"The request for VAT validation was not processed. VIES service has responded"
" with the following error: %s"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "VAT"
msgstr "ДДС"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_company__vat_check_vies
#: model:ir.model.fields,field_description:base_vat.field_res_config_settings__vat_check_vies
msgid "Verify VAT Numbers"
msgstr "Проверете номера по ДДС"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid "Verify VAT numbers using the European VIES service"
msgstr "Проверете номерата по ДДС чрез европейската услуга VIES"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__vies_vat_to_check
#: model:ir.model.fields,field_description:base_vat.field_res_users__vies_vat_to_check
msgid "Vies Vat To Check"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "XXXXXXXXX [9 digits] and it should respect the Luhn algorithm checksum"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "either 11 digits for CPF or 14 digits for CNPJ"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
msgid "fiscal position [%s]"
msgstr ""

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
msgid "partner [%s]"
msgstr ""
