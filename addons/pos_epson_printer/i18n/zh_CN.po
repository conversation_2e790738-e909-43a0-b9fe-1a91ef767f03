# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_epson_printer
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid "Cashdrawer"
msgstr "钱箱"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid ""
"Check on the printer configuration for the 'Device ID' setting. It should be"
" set to: "
msgstr "检查打印机配置中的 \"设备 ID\"。应设置为："

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Epson Printer IP"
msgstr "Epson 打印机 IP地址"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_printer__epson_printer_ip
msgid "Epson Printer IP Address"
msgstr "Epson打印机IP地址"

#. module: pos_epson_printer
#. odoo-python
#: code:addons/pos_epson_printer/models/pos_printer.py:0
msgid "Epson Printer IP Address cannot be empty."
msgstr "Epson 打印机 IP 地址不能为空。"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid "Epson Receipt Printer IP Address"
msgstr "Epson收据打印机IP地址"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid ""
"If you are on a secure server (HTTPS) please make sure you manually accepted"
" the certificate by accessing %s. "
msgstr "如果您使用的是安全服务器（HTTPS），请确保您通过访问 %s 手动接受了证书。"

#. module: pos_epson_printer
#: model:ir.model.fields,help:pos_epson_printer.field_pos_config__epson_printer_ip
#: model:ir.model.fields,help:pos_epson_printer.field_pos_printer__epson_printer_ip
msgid "Local IP address of an Epson receipt printer."
msgstr "Epson收据打印机的本地IP地址。"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "No paper was detected by the printer"
msgstr "打印机未检测到纸张"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "Please check if the printer has enough paper and is ready to print."
msgstr "请检查打印机是否有足够的纸张并准备好打印。"

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_config
msgid "Point of Sale Configuration"
msgstr "POS配置"

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_printer
msgid "Point of Sale Printer"
msgstr "POS 打印机"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_res_config_settings__pos_epson_printer_ip
msgid "Pos Epson Printer Ip"
msgstr "POS Epson打印机IP地址"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_printer__printer_type
msgid "Printer Type"
msgstr "打印机类型"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "Printing failed"
msgstr "打印失败"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid ""
"The Epson receipt printer will be used instead of the receipt printer "
"connected to the IoT Box."
msgstr "将使用Epson收据打印机代替连接到 IoT Box 的收据打印机."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "The following error code was given by the printer:"
msgstr "打印机给出了以下错误代码："

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "The printer was successfully reached, but it wasn't able to print."
msgstr "已成功连接打印机，但无法打印。"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "To find more details on the error reason, please search online for:"
msgstr "有关错误原因的详细信息，请上网搜索："

#. module: pos_epson_printer
#: model:ir.model.fields.selection,name:pos_epson_printer.selection__pos_printer__printer_type__epson_epos
msgid "Use an Epson printer"
msgstr "使用Epson打印机"
