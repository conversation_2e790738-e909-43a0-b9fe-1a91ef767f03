# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_wishlist
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:41+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.snippet_options
msgid ""
"<i class=\"fa fa-fw fa-heart\"/>\n"
"            Wishlist"
msgstr ""
"<i class=\"fa fa-fw fa-heart\"/>\n"
"            Lista de desejos"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_add_to_wishlist
msgid ""
"<i class=\"fa fa-heart-o me-2\" role=\"img\" aria-label=\"Add to wishlist\"/>\n"
"                    Add to wishlist"
msgstr ""
"<i class=\"fa fa-heart-o me-2\" role=\"img\" aria-label=\"Add to wishlist\"/>\n"
"                    Adicionar à lista de desejos"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "<small><i class=\"fa fa-trash-o\"/> Remove</small>"
msgstr "<small><i class=\"fa fa-trash-o\"/> Remover </small>"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid ""
"<span class=\"fa fa-fw fa-shopping-cart\"/>\n"
"                                                <span class=\"d-none d-md-inline\">Add</span>"
msgstr ""
"<span class=\"fa fa-fw fa-shopping-cart\"/>\n"
"                                                <span class=\"d-none d-md-inline\">Adicionar</span>"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.add_to_wishlist
msgid "<span class=\"fa fa-heart\" role=\"img\" aria-label=\"Add to wishlist\"/>"
msgstr "<span class=\"fa fa-heart\" role=\"img\" aria-label=\"Add to wishlist\"/>"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__active
msgid "Active"
msgstr "Ativo"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Add product to my cart but keep it in my wishlist"
msgstr ""
"Adicionar produtos ao meu carrinho, mas manter em minha lista de desejos"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.add_to_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.snippet_options
msgid "Add to Wishlist"
msgstr "Adicionar à lista de desejos"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_add_to_wishlist
msgid "Add to wishlist"
msgstr "Adicionar à lista de desejos"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_res_partner
msgid "Contact"
msgstr "Contato"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Contact Us"
msgstr "Entre em contato"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__create_date
msgid "Created on"
msgstr "Criado em"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__currency_id
msgid "Default Currency"
msgstr "Moeda padrão"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: website_sale_wishlist
#: model:ir.model.constraint,message:website_sale_wishlist.constraint_product_wishlist_product_unique_partner_id
msgid "Duplicated wishlisted product for this partner."
msgstr "Produto duplicado na lista de desejos para este usuário."

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__id
msgid "ID"
msgstr "ID"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "My Wishlist"
msgstr "Minha lista de desejos"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__partner_id
msgid "Owner"
msgstr "Proprietário"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__price
msgid "Price"
msgstr "Preço"

#. module: website_sale_wishlist
#: model:ir.model.fields,help:website_sale_wishlist.field_product_wishlist__price
msgid "Price of the product when it has been added in the wishlist"
msgstr "Preço do produto quando ele foi adicionado na lista de desejos"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__pricelist_id
msgid "Pricelist"
msgstr "Lista de preços"

#. module: website_sale_wishlist
#: model:ir.model.fields,help:website_sale_wishlist.field_product_wishlist__pricelist_id
msgid "Pricelist when added"
msgstr "Lista de preços quando adicionado"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_product_template
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__product_id
msgid "Product"
msgstr "Produto"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_product_product
msgid "Product Variant"
msgstr "Variante do produto"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_product_wishlist
msgid "Product Wishlist"
msgstr "Lista de desejos de produtos"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Product image"
msgstr "Imagem do produto"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_cart_lines
msgid "Save for Later"
msgstr "Salvar para mais tarde"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Shop Wishlist"
msgstr "Lista de desejos da loja"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.snippet_options
msgid "Show/hide empty wishlist"
msgstr "Mostrar/ocultar lista de desejos vazia"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_res_users
msgid "User"
msgstr "Usuário"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__website_id
msgid "Website"
msgstr "Site"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_res_partner__wishlist_ids
#: model:ir.model.fields,field_description:website_sale_wishlist.field_res_users__wishlist_ids
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.header_wishlist_link
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.snippet_options
msgid "Wishlist"
msgstr "Lista de desejos"
