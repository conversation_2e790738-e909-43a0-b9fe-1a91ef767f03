.o_hr_holidays_hierarchy {
    margin-left: calc(var(--formView-sheet-padding-x) * -1);
    margin-right: calc(var(--formView-sheet-padding-x) * -1);
    margin-bottom: calc(var(--formView-sheet-padding-x) * -1);
    padding: var(--formView-sheet-padding-x);
    background: $o-webclient-background-color;
    border-top: $border-width solid $border-color;
    overflow-x: auto;

    .o_hr_holidays_title {
        padding: 0px 0px 0px  86px;
        font-size: 18px;
    }

    .o_hr_holidays_hierarchy_readonly {
        padding: 40px 0px 0px 40px;
    }

    .o_hr_holidays_plan_level_container {
        counter-reset: o-hr-holidays-accrual-plan-level-counter;

        .o-kanban-button-new {
            padding: 2px 12px;
            margin: 0px 0px 0px 44px;
            border-radius: 25px;
        }

        .o_kanban_renderer.o_kanban_ungrouped .o_kanban_record {
            counter-increment: o-hr-holidays-accrual-plan-level-counter;
            flex: 0 0 100%;
            padding: 0px 0px 0px 100px;

            // Timeline Border
            &:before {
                content: '';
                display: block;
                @include o-position-absolute;
                height: 100%;
                margin-left: 8px;
                border-left: $border-width dashed $border-color;
            }

            .o_hr_holidays_plan_level_level::before {
                content: counter(o-hr-holidays-accrual-plan-level-counter);
            }

            // Whole record
            .o_hr_holidays_body {
                margin-left: 16px;
                padding-top: 28px;

                // Left side 'Level'
                .o_hr_holidays_timeline {
                    --bg-opacity: 1;

                    @include o-position-absolute($top: 32px, $left: 6px);
                    width: 90px;
                    padding: 3px 0px;
                    border-radius: 3px;
                    border: $border-width solid $border-color;
                    background: $o-view-background-color;
                }

                // Actual kanban card
                .o_hr_holidays_card {
                    --bg-opacity: 1;

                    position: relative;
                    margin-left: 22px;
                    margin-right: 2px;
                    width: 500px;
                    border-radius: 3px;
                    border: $border-width solid $border-color;
                    background: $o-view-background-color;

                    // Triangle
                    &:before {
                        content: '';
                        @include o-position-absolute($top: 12px, $left: -8px);
                        width: 14px;
                        height: 14px;
                        background-color: $o-view-background-color;
                        border: $border-width solid $border-color;
                        border-top: 0;
                        border-right: 0;
                        transform: rotate(45deg);
                    }

                    // Circle
                    &:after {
                        content: '';
                        @include o-position-absolute($top: 14px, $left: -36px);
                        width: 12px;
                        height: 12px;
                        border: 2px solid $o-brand-primary;
                        border-radius: 10px;
                        background: $o-view-background-color;
                    }

                    .content {
                        position: relative;
                        padding: 5px 7px;
                        font-size: 14px;
                    }
                }
            }
        }
    }
}
.o_radio_item .o_form_label {
    font-weight: normal;
}
