# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_collect
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:41+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Abe Manyo, 2025\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale_collect
#: model_terms:payment.provider,pending_msg:website_sale_collect.payment_provider_on_site
msgid ""
"<i>Your order has been confirmed.</i><br>Please come to the store to pay for"
" your products."
msgstr ""
"<i><PERSON><PERSON><PERSON> Anda telah dikonfirmasi.</i><br><PERSON><PERSON><PERSON> datang ke toko untuk "
"membayar produk-produk Anda."

#. module: website_sale_collect
#: model_terms:ir.ui.view,arch_db:website_sale_collect.payment_confirmation_status
msgid "<span class=\"text-muted\"> (In-store pickup)</span>"
msgstr "<span class=\"text-muted\"> (Pick-up di toko)</span>"

#. module: website_sale_collect
#. odoo-javascript
#: code:addons/website_sale_collect/static/src/js/click_and_collect_availability/click_and_collect_availability.xml:0
#: code:addons/website_sale_collect/static/src/js/location_selector/location/location.xml:0
msgid "Available"
msgstr "Tersedia"

#. module: website_sale_collect
#. odoo-javascript
#: code:addons/website_sale_collect/static/src/js/click_and_collect_availability/click_and_collect_availability.xml:0
msgid "Check availability"
msgstr "Periksa ketersediaan"

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfig"

#. module: website_sale_collect
#: model_terms:ir.ui.view,arch_db:website_sale_collect.res_config_settings_form
msgid "Configure Pickup Locations"
msgstr "Konfigurasikan Lokasi Pickup"

#. module: website_sale_collect
#: model:ir.model.fields,field_description:website_sale_collect.field_payment_provider__custom_mode
msgid "Custom Mode"
msgstr "Mode Kustom"

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/models/res_config_settings.py:0
msgid "Delivery Methods"
msgstr "Metode Pengiriman"

#. module: website_sale_collect
#: model:ir.model.fields,field_description:website_sale_collect.field_website__in_store_dm_id
msgid "In-store Delivery Method"
msgstr "Metode Pengiriman Di-toko"

#. module: website_sale_collect
#. odoo-javascript
#: code:addons/website_sale_collect/static/src/js/click_and_collect_availability/click_and_collect_availability.xml:0
msgid "Not available"
msgstr "Tidak tersedia"

#. module: website_sale_collect
#. odoo-javascript
#: code:addons/website_sale_collect/static/src/js/click_and_collect_availability/click_and_collect_availability.xml:0
#: code:addons/website_sale_collect/static/src/js/location_selector/location/location.xml:0
msgid "Only"
msgstr "Hanya"

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/models/sale_order.py:0
msgid "Only %(new_qty)s available"
msgstr "Hanya %(new_qty)s yang tersedia"

#. module: website_sale_collect
#: model:ir.model.fields,field_description:website_sale_collect.field_stock_warehouse__opening_hours
msgid "Opening Hours"
msgstr "Jam Buka"

#. module: website_sale_collect
#. odoo-javascript
#: code:addons/website_sale_collect/static/src/js/location_selector/location/location.xml:0
msgid "Out of stock"
msgstr "Out of stock"

#. module: website_sale_collect
#: model:payment.provider,name:website_sale_collect.payment_provider_on_site
msgid "Pay on Site"
msgstr "Bayar di Tempat"

#. module: website_sale_collect
#: model:ir.model.fields.selection,name:website_sale_collect.selection__payment_provider__custom_mode__on_site
#: model:payment.method,name:website_sale_collect.payment_method_pay_on_site
msgid "Pay on site"
msgstr "Bayar di situs"

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_payment_provider
msgid "Payment Provider"
msgstr "Penyedia Pembayaran"

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transaksi Tagihan"

#. module: website_sale_collect
#. odoo-javascript
#: code:addons/website_sale_collect/static/src/js/click_and_collect_availability/click_and_collect_availability.xml:0
#: model:delivery.carrier,name:website_sale_collect.carrier_pick_up_in_store
#: model:ir.model.fields.selection,name:website_sale_collect.selection__delivery_carrier__delivery_type__in_store
#: model:product.template,name:website_sale_collect.product_pick_up_in_store_product_template
msgid "Pick up in store"
msgstr "Pick up di toko"

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/controllers/main.py:0
msgid "Please choose a store to collect your order."
msgstr "Silakan pilih toko untuk mengumpulkan pesanan Anda."

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_product_template
msgid "Product"
msgstr "Produk"

#. module: website_sale_collect
#: model:ir.model.fields,field_description:website_sale_collect.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Pemberi"

#. module: website_sale_collect
#: model_terms:ir.ui.view,arch_db:website_sale_collect.unavailable_products_warning
msgid "Remove from cart"
msgstr "Hapus dari keranjang"

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_sale_order
msgid "Sales Order"
msgstr "Order Penjualan"

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Metode Pengiriman"

#. module: website_sale_collect
#: model_terms:ir.ui.view,arch_db:website_sale_collect.unavailable_products_warning
msgid "Some of the products are not available at"
msgstr "Beberapa produk tidak tersedia di"

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/controllers/main.py:0
#: code:addons/website_sale_collect/models/sale_order.py:0
msgid "Some products are not available in the selected store."
msgstr "Beberapa produk tidak tersedia di toko terpilih."

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/controllers/main.py:0
msgid "Sorry, we are unable to ship your order."
msgstr "Maaf, kami tidak dapat mengirimkan pesanan Anda."

#. module: website_sale_collect
#: model:ir.model.fields,field_description:website_sale_collect.field_delivery_carrier__warehouse_ids
#: model_terms:ir.ui.view,arch_db:website_sale_collect.delivery_carrier_form
msgid "Stores"
msgstr "Toko-Toko"

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/models/delivery_carrier.py:0
msgid "The delivery method and a warehouse must share the same company"
msgstr "Metode pengiriman dan gudang harus memiliki perusahaan yang sama"

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/models/delivery_carrier.py:0
msgid "The delivery method must have at least one warehouse to be published."
msgstr ""
"Metode pengiriman harus memiliki setidaknya satu gudang untuk "
"dipublikasikan."

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_stock_warehouse
msgid "Warehouse"
msgstr "Gudang"

#. module: website_sale_collect
#: model:ir.model,name:website_sale_collect.model_website
msgid "Website"
msgstr "Website"

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/controllers/payment.py:0
msgid ""
"You can only pay on site when selecting the pick up in store delivery "
"method."
msgstr ""
"Anda hanya dapat membayar di tempat saat memilih metode pengiriman pick up "
"di toko."

#. module: website_sale_collect
#: model_terms:payment.provider,auth_msg:website_sale_collect.payment_provider_on_site
msgid "Your payment has been authorized."
msgstr "Tagihan Anda telah disahkan."

#. module: website_sale_collect
#: model_terms:payment.provider,cancel_msg:website_sale_collect.payment_provider_on_site
msgid "Your payment has been cancelled."
msgstr "Pembayaran Anda telah dibatalkan."

#. module: website_sale_collect
#: model_terms:payment.provider,done_msg:website_sale_collect.payment_provider_on_site
msgid "Your payment has been successfully processed."
msgstr "Pembayaran Anda sukses diproses."

#. module: website_sale_collect
#. odoo-javascript
#: code:addons/website_sale_collect/static/src/js/click_and_collect_availability/click_and_collect_availability.xml:0
#: code:addons/website_sale_collect/static/src/js/location_selector/location/location.xml:0
msgid "available"
msgstr "tersedia"

#. module: website_sale_collect
#. odoo-python
#: code:addons/website_sale_collect/models/payment_provider.py:0
msgid "no in-store delivery methods available"
msgstr "tidak ada metode pengiriman di-toko yang tersedia"
