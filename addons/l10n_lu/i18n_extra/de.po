# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_lu
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-06-19 13:53+0000\n"
"PO-Revision-Date: 2019-08-30 08:50+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1a_overall_turnover
msgid "012 - Overall turnover"
msgstr "012 - Gesamtumsatz"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_2_export
msgid "014 - Exports"
msgstr "014 - Ausfuhren"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_3_other_exemptions_art_43
msgid "015 - Other exemptions"
msgstr "015 - Andere Befreiungen"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_4_other_exemptions_art_44_et_56quater
msgid "016 - Other exemptions"
msgstr "016 - Andere Befreiungen"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_5_manufactured_tobacco_vat_collected
msgid ""
"017 - Manufactured tobacco whose VAT was collected at the source or at the "
"exit of the tax..."
msgstr ""
"017 - Tabakwaren, deren Mehrwertsteuer an der Quelle oder am Ausgang des "
"Steuerlagers gemeinsam mit den Verbrauchsteuern erhoben wurde"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_6_a_subsequent_to_intra_community
msgid ""
"018 - Supply, subsequent to intra-Community acquisitions of goods, in the "
"context of triangular transactions, when the customer identified,..."
msgstr ""
"018 - An innergemeinschaftliche Erwerbe anschließende Lieferungen im Rahmen "
"von Dreiecksgeschäften..."

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_6_d_supplies_other_referred
msgid ""
"019 - Other supplies carried out (for which the place of supply is) abroad"
msgstr "019 - Andere im Ausland getätigte (steuerpflichtige) Umsätze"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_exemptions_deductible_amounts
msgid "021 - Exemptions and deductible amounts"
msgstr "021 - Steuerbefreiungen und abzugsfähige Beträge"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1c_taxable_turnover
msgid "022 - Taxable turnover"
msgstr "022 - Steuerpflichtiger Umsatz"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_3
msgid "031 - base 3%"
msgstr "031 - Besteuerungsgrundlage 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_0
msgid "033 - base 0%"
msgstr "033 - Besteuerungsgrundlage 0%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_breakdown_taxable_turnover_base
msgid "037 - Breakdown of taxable turnover – base"
msgstr "037 - Steuerpflichtiger Umsatz: Aufteilung - Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_tax_3
msgid "040 - tax 3%"
msgstr "040 - MwSt. 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_breakdown_taxable_turnover_tax
msgid "046 - Breakdown of taxable turnover – tax"
msgstr "046 - Steuerpflichtiger Umsatz: Aufteilung - MwSt."

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_3
msgid "049 - base 3%"
msgstr "049 - Besteuerungsgrundlage 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_intra_community_acqui_of_goods_base
msgid "051 - Intra-Community acquisitions of goods – base"
msgstr ""
"051 - Innergemeinschaftliche Erwerbe von Gegenständen - "
"Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_tax_3
msgid "054 - tax 3%"
msgstr "054 - MwSt. 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_intra_community_acquisitions_goods_tax
msgid "056 - Intra-Community acquisitions of goods – tax"
msgstr "056 - Innergemeinschaftliche Erwerbe von Gegenständen - MwSt."

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_3
msgid "059 - for business purposes: base 3%"
msgstr "059 - für Zwecke des Unternehmens: Besteuerungsgrundlage 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_3
msgid "063 - for non-business purposes: base 3%"
msgstr "063 - für unternehmensfremde Zwecke: Besteuerungsgrundlage 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_importation_of_goods_base
msgid "065 - Importation of goods – base"
msgstr "065 - Einfuhren von Gegenständen - Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_3
msgid "068 - for business purposes: tax 3%"
msgstr "068 - für Zwecke des Unternehmens: MwSt. 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_3
msgid "073 - for non-business purposes: tax 3%"
msgstr "073 - für unternehmensfremde Zwecke: MwSt. 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2h_total_tax_due
msgid "076 - Total tax due"
msgstr "076 - Gesamtbetrag der Steuer"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_4_due_respect_application_goods
msgid "090 - Due in respect of the application of goods for business purposes"
msgstr ""
"090 - Erklärte Mehrwertsteuer für die Zuordnung von Gegenständen zu Zwecken "
"des Unternehmens"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_6_paid_joint_several_guarantee
msgid "092 - Paid as joint and several guarantee"
msgstr "092 - Als solidarisch haftender Bürge bezahlte MwSt."

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_total_input_tax
msgid "093 - Total input tax"
msgstr "093 - Gesamtbetrag Vorsteuer"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3b1_rel_trans
msgid ""
"094 - relating to transactions which are exempt pursuant to articles 44 and "
"56quater"
msgstr ""
"094 - Nicht abziehbare Vorsteuer betreffend die gemäß Art. 44 und Art. "
"56quater steuerfreien Umsätze"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3b2_ded_prop
msgid ""
"095 - where the deductible proportion determined in accordance to article 50"
" is applied"
msgstr ""
"095 - Nicht abziehbare Vorsteuer in Anwendung der in Art. 50 vorgesehenen "
"Prorata-Regel"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3b2_input_tax_margin
msgid ""
"096 - Non recoverable input tax in accordance with Art. 56ter-1(7) and "
"56ter-2(7) (when applying the margin scheme)"
msgstr ""
"096 - Nicht abziehbare Vorsteuer in Anwendung von Art. 56ter-1/7 und "
"56ter-2/7 "

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3b_total_input_tax_nd
msgid "097 - Total input tax non-deductible"
msgstr "097 - Gesamtbetrag der nicht abziehbaren Vorsteuer"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3c_total_input_tax_deductible
msgid "102 - Total input tax deductible"
msgstr "102 - Gesamtbetrag der abziehbaren Vorsteuer"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_4a_total_tax_due
msgid "103 - Total tax due"
msgstr "103 - Gesamtbetrag der Steuer"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_4a_total_input_tax_deductible
msgid "104 - Total input tax deductible"
msgstr "104 - Gesamtbetrag der abziehbaren Vorsteuer"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_4c_exceeding_amount
msgid "105 - Exceeding amount"
msgstr "105 - Überschuss"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2c_acquisitions_triangular_transactions_base
msgid "152 - Acquisitions, in the context of triangular transactions – base"
msgstr ""
"152 - Im Rahmen von Dreiecksgeschäften getätigte Erwerbe - "
"Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_188
msgid ""
"188 - Appendix A - Expenses for other work carried out by third parties"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_190
msgid "190 - Appendix A - Car expenses"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_exempt
msgid "194 - base exempt"
msgstr "194 - Besteuerungsgrundlage steuerbefreit"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_exempt
msgid "195 - for business purposes: base exempt"
msgstr ""
"195 - für Zwecke des Unternehmens: Besteuerungsgrundlage steuerbefreit"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_exempt
msgid "196 - for non-business purposes: base exempt"
msgstr ""
"196 - für unternehmensfremde Zwecke: Besteuerungsgrundlage steuerbefreit"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_6_c_supplies_scope_special_arrangement
msgid ""
"226 - Supplies carried out within the scope of the special arrangement of "
"art. 56sexies"
msgstr ""
"226 - Im Rahmen der Sonderregelung von Artikel 56sexies getätigte Umsätze"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2g_special_arrangement
msgid "227 - Special arrangement for tax suspension: adjustment"
msgstr "227 - Sonderregelung zur Steueraussetzung: Berichtigung"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_7_adjusted_tax_special_arrangement
msgid "228 - Adjusted tax - special arrangement for tax suspension"
msgstr "228 - Berichtigte Steuer - Sonderregelung zur Steueraussetzung"

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_239
msgid "239 - Appendix A - Gross salaries"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_244
msgid "244 - Appendix A - Gross wages"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_247
msgid "247 - Appendix A - Occasional salaries"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_250
msgid ""
"250 - Appendix A - Compulsory social security contributions (employer's "
"share)"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_253
msgid "253 - Appendix A - Accident insurance"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_260
msgid "260 - Appendix A - Staff travel and representation expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_269
msgid "269 - Appendix A - Accounting and bookkeeping fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_283
msgid "283 - Appendix A - Employer's travel and representation expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_285
msgid "285 - Appendix A - Electricity"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_289
msgid "289 - Appendix A - Gas"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_293
msgid "293 - Appendix A - Employer's travel and representation expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_301
msgid "301 - Appendix A - Telecommunications"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_305
msgid ""
"305 - Appendix A - Renting/leasing of immovable property with application of"
" VAT"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_307
msgid ""
"307 - Appendix A - Renting/leasing of immovable property with no application"
" of VAT"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_310
msgid ""
"310 - Appendix A - Renting/leasing of permanently installed equipment and "
"machinery"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_316
msgid "316 - Appendix A - Property tax"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_324
msgid "324 - Appendix A - Business tax"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_326
msgid "326 - Appendix A - Interest paid for long-term debts"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_327
msgid "327 - Appendix A - Interest paid for short-term debts"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_328
msgid "328 - Appendix A - Other financial costs"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_330
msgid "330 - Appendix A - Stock and business equipment insurance"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_331
msgid ""
"331 - Appendix A - Public and professional third party liability insurance"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_332
msgid "332 - Appendix A - Office expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_336
msgid ""
"336 - Appendix A - Fees and subscriptions paid to professional associations "
"and learned societies"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_337
msgid "337 - Appendix A - Papers and periodicals for business purposes"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_343
msgid "343 - Appendix A - Shipping and transport expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_345
msgid "345 - Appendix A - Work clothes"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_347
msgid "347 - Appendix A - Advertising and publicity"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_349
msgid "349 - Appendix A - Packaging"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_351
msgid "351 - Appendix A - Repair and maintenance of equipment and machinery"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_353
msgid "353 - Appendix A - Other repairs"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_355
msgid ""
"355 - Appendix A - New acquisitions (tools and equipment) if their cost can "
"be fully allocated to the year of acquisition or creation"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_358
msgid "358 - Appendix A - Custom (value)"
msgstr ""

#. module: l10n_lu
#: model:account.account.tag,name:l10n_lu.account_tag_appendix_361
msgid "361 - Appendix A - Total 'Appendix to Operational expenditures'"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_importation_of_goods_tax
msgid "407 - Importation of goods – tax"
msgstr "407 - Einfuhren von Gegenständen - MwSt."

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_supply_of_service_for_customer
msgid ""
"409 - Supply of services for which the customer is liable for the payment of"
" VAT – base"
msgstr ""
"409 - Vom Empfänger als Steuerschuldner zu erklärende Dienstleistungen  - "
"Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_supply_of_service_for_customer_liable_for_payment_tax
msgid ""
"410 - Supply of services for which the customer is liable for the payment of"
" VAT – tax"
msgstr ""
"410 - Vom Empfänger als Steuerschuldner zu erklärende Dienstleistungen  - "
"MwSt."

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_7_inland_supplies_for_customer
msgid ""
"419 - Inland supplies for which the customer is liable for the payment of "
"VAT"
msgstr "419 - Umsätze im Inland, für die der Empfänger Steuerschuldner ist"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_6_b1_non_exempt_customer_vat
msgid ""
"423 - not exempt in the MS where the customer is liable for payment of VAT"
msgstr ""
"423 - Dienstleistungen, die im Mitgliedstaat des Empfängers, der dort für "
"Zwecke der MwSt. erfasst und Steuerschuldner ist, nicht steuerbefreit sind"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_6_b2_exempt_ms_customer
msgid "424 - exempt in the MS where the customer is identified"
msgstr ""
"424 - Dienstleistungen, die im Mitgliedstaat des Empfängers, der dort für "
"Zwecke der MwSt. erfasst ist, steuerbefreit sind"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_3
msgid "431 - not exempt within the territory: base 3%"
msgstr "431 - nicht steuerbefreit im Inland: Besteuerungsgrundlage 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_3
msgid "432 - not exempt within the territory: tax 3%"
msgstr "432 - nicht steuerbefreit im Inland: MwSt. 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_b_exempt
msgid "435 - exempt within the territory: exempt"
msgstr "435 - steuerbefreit im Inland: steuerbefreit"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_base
msgid "436 - base"
msgstr "436 - Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_3
msgid "441 - not established or residing within the Community: base 3%"
msgstr ""
"441 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: Besteuerungsgrundlage 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_3
msgid "442 - not established or residing within the Community: tax 3%"
msgstr ""
"442 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: MwSt. 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_exempt
msgid "445 - not established or residing within the Community: exempt"
msgstr ""
"445 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: steuerbefreit"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1a_total_sale
msgid "454 - Total Sales / Receipts"
msgstr "454 - Gesamtbetrag der Entgelte"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1a_app_goods_non_bus
msgid ""
"455 - Application of goods for non-business use and for business purposes"
msgstr ""
"455 - Entnahmen von Gegenständen für Zwecke des Unternehmens und für "
"unternehmensfremde Zwecke"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1a_non_bus_gs
msgid "456 - Non-business use of goods and supply of services free of charge"
msgstr "456 - Erbringung von Dienstleistungen für unternehmensfremde Zwecke"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_1_intra_community_goods_pi_vat
msgid ""
"457 - Intra-Community supply of goods to persons identified for VAT purposes"
" in another Member State (MS)"
msgstr ""
"457 - Innergemeinschaftliche Lieferungen an Personen, die eine Id.-Nummer in"
" einem anderen Mitgliedstaat besitzen"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_1_invoiced_by_other_taxable_person
msgid "458 - Invoiced by other taxable persons for goods or services supplied"
msgstr ""
"458 - Von anderen Steuerpflichtigen für Warenlieferungen und "
"Dienstleistungen in Rechnung gestellte Mehrwertsteuer"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_2_due_respect_intra_comm_goods
msgid "459 - Due in respect of intra-Community acquisitions of goods"
msgstr ""
"459 - Erklärte oder bezahlte Mehrwertsteuer für innergemeinschaftliche "
"Erwerbe von Gegenständen"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_3_due_paid_respect_importation_goods
msgid "460 - Due or paid in respect of importation of goods"
msgstr "460 - Erklärte oder bezahlte Mehrwertsteuer für eingeführte Waren"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_5_due_under_reverse_charge
msgid "461 - Due under the reverse charge (see points II.E and F)"
msgstr "461 - Als Schuldner erklärte Mehrwertsteuer (Siehe Punkte II.E und F)"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax
msgid "462 - tax"
msgstr "462 - MwSt."

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base
msgid "463 - base"
msgstr "463 - Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax
msgid "464 - tax"
msgstr "464 - MwSt."

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1a_telecom_service
msgid ""
"471 - Telecommunications services, radio and television broadcasting "
"services..."
msgstr ""
"471 - Telekommunikationsdienstleistungen, Rundfunk- und "
"Fernsehdienstleistungen..."

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1a_other_sales
msgid "472 - Other sales / receipts"
msgstr "472 - Andere Umsätze / Erträge"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_8_supplies_carried_out_domestic
msgid ""
"481 - Supplies carried out within the scope of the domestic SME scheme of "
"article 57bis (7)"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_9_supplies_carried_out_cross_border
msgid ""
"482 - Supplies carried out within the scope of the cross-border SME scheme "
"of article 57quater "
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_17
msgid "701 - base 17%"
msgstr "701 - Besteuerungsgrundlage 17%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_tax_17
msgid "702 - tax 17%"
msgstr "702 - MwSt. 17%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_14
msgid "703 - base 14%"
msgstr "703 - Besteuerungsgrundlage 14%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_tax_14
msgid "704 - tax 14%"
msgstr "704 - MwSt. 14%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_8
msgid "705 - base 8%"
msgstr "705 - Besteuerungsgrundlage 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_tax_8
msgid "706 - tax 8%"
msgstr "706 - MwSt. 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_17
msgid "711 - base 17%"
msgstr "711 - Besteuerungsgrundlage 17%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_tax_17
msgid "712 - tax 17%"
msgstr "712 - MwSt. 17%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_14
msgid "713 - base 14%"
msgstr "713 - Besteuerungsgrundlage 14%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_tax_14
msgid "714 - tax 14%"
msgstr "714 - MwSt. 14%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_8
msgid "715 - base 8%"
msgstr "715 - Besteuerungsgrundlage 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_tax_8
msgid "716 - tax 8%"
msgstr "716 - MwSt. 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_manufactured_tobacco
msgid ""
"719 - of manufactured tobacco (VAT is collected at the exit of the tax "
"warehouse with excise duties)"
msgstr ""
"719 - von Tabakwaren, deren Mehrwertsteuer am Ausgang des Steuerlagers "
"gemeinsam mit den Verbrauchsteuern erhoben wird"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_17
msgid "721 - for business purposes: base 17%"
msgstr "721 - für Zwecke des Unternehmens: Besteuerungsgrundlage 17%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_17
msgid "722 - for business purposes: tax 17%"
msgstr "722 - für Zwecke des Unternehmens: MwSt. 17%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_14
msgid "723 - for business purposes: base 14%"
msgstr "723 - für Zwecke des Unternehmens: Besteuerungsgrundlage 14%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_14
msgid "724 - for business purposes: tax 14%"
msgstr "724 - für Zwecke des Unternehmens: MwSt. 14%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_8
msgid "725 - for business purposes: base 8%"
msgstr "725 - für Zwecke des Unternehmens: Besteuerungsgrundlage 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_8
msgid "726 - for business purposes: tax 8%"
msgstr "726 - für Zwecke des Unternehmens: MwSt. 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_manufactured_tobacco
msgid ""
"729 - of manufactured tobacco (VAT is collected at the exit of the tax "
"warehouse with excise duties)"
msgstr ""
"729 - von Tabakwaren, deren Mehrwertsteuer am Ausgang des Steuerlagers "
"gemeinsam mit den Verbrauchsteuern erhoben wird"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_17
msgid "731 - for non-business purposes: base 17%"
msgstr "731 - für unternehmensfremde Zwecke: Besteuerungsgrundlage 17%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_17
msgid "732 - for non-business purposes: tax 17%"
msgstr "732 - für unternehmensfremde Zwecke: MwSt. 17%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_14
msgid "733 - for non-business purposes: base 14%"
msgstr "733 - für unternehmensfremde Zwecke: Besteuerungsgrundlage 14%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_14
msgid "734 - for non-business purposes: tax 14%"
msgstr "734 - für unternehmensfremde Zwecke: MwSt. 14%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_8
msgid "735 - for non-business purposes: base 8%"
msgstr "735 - für unternehmensfremde Zwecke: Besteuerungsgrundlage 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_8
msgid "736 - for non-business purposes: tax 8%"
msgstr "736 - für unternehmensfremde Zwecke: MwSt. 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_17
msgid "741 - not exempt within the territory: base 17%"
msgstr "741 - nicht steuerbefreit im Inland: Besteuerungsgrundlage 17%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_17
msgid "742 - not exempt within the territory: tax 17%"
msgstr "742 - nicht steuerbefreit im Inland: MwSt. 17%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_14
msgid "743 - not exempt within the territory: base 14%"
msgstr "743 - nicht steuerbefreit im Inland: Besteuerungsgrundlage 14%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_14
msgid "744 - not exempt within the territory: tax 14%"
msgstr "744 - nicht steuerbefreit im Inland: MwSt. 14%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_8
msgid "745 - not exempt within the territory: base 8%"
msgstr "745 - nicht steuerbefreit im Inland: Besteuerungsgrundlage 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_8
msgid "746 - not exempt within the territory: tax 8%"
msgstr "746 - nicht steuerbefreit im Inland: MwSt. 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_17
msgid "751 - not established or residing within the Community: base 17%"
msgstr ""
"751 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: Besteuerungsgrundlage 17%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_17
msgid "752 - not established or residing within the Community: tax 17%"
msgstr ""
"752 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: MwSt. 17%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_14
msgid "753 - not established or residing within the Community: base 14%"
msgstr ""
"753 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: Besteuerungsgrundlage 14%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_14
msgid "754 - not established or residing within the Community: tax 14%"
msgstr ""
"754 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: MwSt. 14%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_8
msgid "755 - not established or residing within the Community: base 8%"
msgstr ""
"755 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: Besteuerungsgrundlage 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_8
msgid "756 - not established or residing within the Community: tax 8%"
msgstr ""
"756 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: MwSt. 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_3_base_17
msgid "761 - suppliers established within the territory: base 17%"
msgstr ""
"761 - erbracht an den Erklärenden von im Inland ansässigen "
"Steuerpflichtigen: Besteuerungsgrundlage 17%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_3_tax_17
msgid "762 - suppliers established within the territory: tax 17%"
msgstr ""
"762 - erbracht an den Erklärenden von im Inland ansässigen "
"Steuerpflichtigen: MwSt. 17%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_base_8
msgid "763 - base 8%"
msgstr "763 - Besteuerungsgrundlage 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_tax_8
msgid "764 - tax 8%"
msgstr "764 - MwSt. 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_3_base
msgid "765 - base"
msgstr "765 - Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_3_tax
msgid "766 - tax"
msgstr "766 - MwSt."

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_base
msgid ""
"767 - Supply of goods for which the purchaser is liable for the payment of "
"VAT - base"
msgstr ""
"767 - Vom Erwerber als Steuerschuldner zu erklärende Lieferungen - "
"Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_tax
msgid ""
"768 - Supply of goods for which the purchaser is liable for the payment of "
"VAT - tax"
msgstr ""
"768 - Vom Erwerber als Steuerschuldner zu erklärende Lieferungen - MwSt."

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_base_17
msgid "769 - base 17%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_tax_17
msgid "770 - tax 17%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_16
msgid "901 - base 16%"
msgstr "901 - Besteuerungsgrundlage 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_tax_16
msgid "902 - tax 16%"
msgstr "902 - MwSt. 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_13
msgid "903 - base 13%"
msgstr "903 - Besteuerungsgrundlage 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_tax_13
msgid "904 - tax 13%"
msgstr "904 - MwSt. 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_7
msgid "905 - base 7%"
msgstr "905 - Besteuerungsgrundlage 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_tax_7
msgid "906 - tax 7%"
msgstr "906 - MwSt. 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_16
msgid "911 - base 16%"
msgstr "911 - Besteuerungsgrundlage 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_tax_16
msgid "912 - tax 16%"
msgstr "912 - MwSt. 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_13
msgid "913 - base 13%"
msgstr "913 - Besteuerungsgrundlage 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_tax_13
msgid "914 - tax 13%"
msgstr "914 - MwSt. 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_7
msgid "915 - base 7%"
msgstr "915 - Besteuerungsgrundlage 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_tax_7
msgid "916 - tax 7%"
msgstr "916 - MwSt. 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_16
msgid "921 - for business purposes: base 16%"
msgstr "921 - für Zwecke des Unternehmens: Besteuerungsgrundlage 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_16
msgid "922 - for business purposes: tax 16%"
msgstr "922 - für Zwecke des Unternehmens: MwSt. 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_13
msgid "923 - for business purposes: base 13%"
msgstr "923 - für Zwecke des Unternehmens: Besteuerungsgrundlage 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_13
msgid "924 - for business purposes: tax 13%"
msgstr "924 - für Zwecke des Unternehmens: MwSt. 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_7
msgid "925 - for business purposes: base 7%"
msgstr "925 - für Zwecke des Unternehmens: Besteuerungsgrundlage 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_7
msgid "926 - for business purposes: tax 7%"
msgstr "926 - für Zwecke des Unternehmens: MwSt. 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_16
msgid "931 - for non-business purposes: base 16%"
msgstr "931 - für unternehmensfremde Zwecke: Besteuerungsgrundlage 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_16
msgid "932 - for non-business purposes: tax 16%"
msgstr "932 - für unternehmensfremde Zwecke: MwSt. 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_13
msgid "933 - for non-business purposes: base 13%"
msgstr "933 - für unternehmensfremde Zwecke: Besteuerungsgrundlage 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_13
msgid "934 - for non-business purposes: tax 13%"
msgstr "934 - für unternehmensfremde Zwecke: MwSt. 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_7
msgid "935 - for non-business purposes: base 7%"
msgstr "935 - für unternehmensfremde Zwecke: Besteuerungsgrundlage 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_7
msgid "936 - for non-business purposes: tax 7%"
msgstr "936 - für unternehmensfremde Zwecke: MwSt. 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_16
msgid "941 - not exempt within the territory: base 16%"
msgstr "941 - nicht steuerbefreit im Inland: Besteuerungsgrundlage 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_16
msgid "942 - not exempt within the territory: tax 16%"
msgstr "942 - nicht steuerbefreit im Inland: MwSt. 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_13
msgid "943 - not exempt within the territory: base 13%"
msgstr "943 - nicht steuerbefreit im Inland: Besteuerungsgrundlage 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_13
msgid "944 - not exempt within the territory: tax 13%"
msgstr "944 - nicht steuerbefreit im Inland: MwSt. 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_7
msgid "945 - not exempt within the territory: base 7%"
msgstr "945 - nicht steuerbefreit im Inland: Besteuerungsgrundlage 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_7
msgid "946 - not exempt within the territory: tax 7%"
msgstr "946 - nicht steuerbefreit im Inland: MwSt. 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_16
msgid "951 - not established or residing within the Community: base 16%"
msgstr ""
"951 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: Besteuerungsgrundlage 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_16
msgid "952 - not established or residing within the Community: tax 16%"
msgstr ""
"952 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: MwSt. 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_13
msgid "953 - not established or residing within the Community: base 13%"
msgstr ""
"953 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: Besteuerungsgrundlage 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_13
msgid "954 - not established or residing within the Community: tax 13%"
msgstr ""
"954 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: MwSt. 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_7
msgid "955 - not established or residing within the Community: base 7%"
msgstr ""
"955 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: Besteuerungsgrundlage 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_7
msgid "956 - not established or residing within the Community: tax 7%"
msgstr ""
"956 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht in der "
"Gemeinschaft ansässig sind: MwSt. 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_3_base_16
msgid "961 - suppliers established within the territory: base 16%"
msgstr ""
"961 - erbracht an den Erklärenden von im Inland ansässigen "
"Steuerpflichtigen: Besteuerungsgrundlage 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_3_tax_16
msgid "962 - suppliers established within the territory: tax 16%"
msgstr ""
"962 - erbracht an den Erklärenden von im Inland ansässigen "
"Steuerpflichtigen: MwSt. 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_base_7
msgid "963 - base 7%"
msgstr "963 - Besteuerungsgrundlage 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_tax_7
msgid "964 - tax 7%"
msgstr "964 - MwSt. 7%"

#. module: l10n_lu
#: model:ir.model,name:l10n_lu.model_account_chart_template
msgid "Account Chart Template"
msgstr "Kontenplanvorlage"

#. module: l10n_lu
#: model:account.report.column,name:l10n_lu.tax_report_section_1_balance
#: model:account.report.column,name:l10n_lu.tax_report_section_2_balance
#: model:account.report.column,name:l10n_lu.tax_report_sections_3_4_balance
msgid "Balance"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.l10n_lu_tax_report_assessment_turnover
msgid "I. ASSESSMENT OF TAXABLE TURNOVER"
msgstr "I. BERECHNUNG DES STEUERPFLICHTIGEN UMSATZES"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.l10n_lu_tax_report_assessment_tax_due
msgid "II. ASSESSMENT OF TAX DUE"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3_assessment_deducible_tax
msgid "III. ASSESSMENT OF DEDUCTIBLE TAX (input tax)"
msgstr "III. BERECHNUNG DER ABZIEHBAREN VORSTEUER"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_4_tax_tobe_paid_or_reclaimed
msgid "IV. TAX TO BE PAID OR TO BE RECLAIMED"
msgstr "IV. BERECHNUNG DES ÜBERSCHUSSES"

#. module: l10n_lu
#: model:ir.ui.menu,name:l10n_lu.account_reports_lu_statements_menu
msgid "Luxembourg"
msgstr ""

#. module: l10n_lu
#: model:account.report,name:l10n_lu.l10n_lu_tax_report_section_1
msgid "Section I"
msgstr ""

#. module: l10n_lu
#: model:account.report,name:l10n_lu.l10n_lu_tax_report_section_2
msgid "Section II"
msgstr ""

#. module: l10n_lu
#: model:account.report,name:l10n_lu.l10n_lu_tax_report_sections_3_4
msgid "Sections III, IV"
msgstr ""

#. module: l10n_lu
#: model:account.report,name:l10n_lu.tax_report
msgid "Tax Report"
msgstr ""
