# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_asiapay
# 
# Translators:
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: payment_asiapay
#. odoo-python
#: code:addons/payment_asiapay/models/payment_transaction.py:0
msgid ""
"An error occurred during the processing of your payment (success code "
"%(success_code)s; primary response code %(response_code)s). Please try "
"again."
msgstr ""
"Виникла помилка під час обробки вашого платежу (код доступу "
"%(success_code)s; первинний код відповіді %(response_code)s). Спробуйте ще "
"раз."

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__code__asiapay
msgid "AsiaPay"
msgstr "AsiaPay"

#. module: payment_asiapay
#: model:ir.model.fields,field_description:payment_asiapay.field_payment_provider__asiapay_merchant_id
msgid "AsiaPay Merchant ID"
msgstr "AsiaPay Merchant ID"

#. module: payment_asiapay
#: model:ir.model.fields,field_description:payment_asiapay.field_payment_provider__asiapay_secure_hash_function
msgid "AsiaPay Secure Hash Function"
msgstr "Функція хешу безпеки AsiaPay"

#. module: payment_asiapay
#: model:ir.model.fields,field_description:payment_asiapay.field_payment_provider__asiapay_secure_hash_secret
msgid "AsiaPay Secure Hash Secret"
msgstr "Ключ безпеки хешу AsiaPay"

#. module: payment_asiapay
#: model:ir.model.fields,field_description:payment_asiapay.field_payment_provider__asiapay_brand
msgid "Asiapay Brand"
msgstr "Бренд Asiapay"

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__asiapay_brand__bimopay
msgid "BimoPay"
msgstr "BimoPay"

#. module: payment_asiapay
#: model_terms:ir.ui.view,arch_db:payment_asiapay.payment_provider_form
msgid "Brand"
msgstr "Бренд"

#. module: payment_asiapay
#: model:ir.model.fields,field_description:payment_asiapay.field_payment_provider__code
msgid "Code"
msgstr "Код"

#. module: payment_asiapay
#: model_terms:ir.ui.view,arch_db:payment_asiapay.payment_provider_form
msgid "Merchant ID"
msgstr "ID продавця"

#. module: payment_asiapay
#. odoo-python
#: code:addons/payment_asiapay/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Не знайдено жодної транзакції, що відповідає референсу %s."

#. module: payment_asiapay
#. odoo-python
#: code:addons/payment_asiapay/models/payment_provider.py:0
msgid "Only one currency can be selected by AsiaPay account."
msgstr "Лише одну валюту можна вибрати на рахунок AsiaPay."

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__asiapay_brand__paydollar
msgid "PayDollar"
msgstr "PayDollar"

#. module: payment_asiapay
#: model:ir.model,name:payment_asiapay.model_payment_provider
msgid "Payment Provider"
msgstr "Провайдер платежу"

#. module: payment_asiapay
#: model:ir.model,name:payment_asiapay.model_payment_transaction
msgid "Payment Transaction"
msgstr "Платіжна операція"

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__asiapay_brand__pesopay
msgid "PesoPay"
msgstr "PesoPay"

#. module: payment_asiapay
#. odoo-python
#: code:addons/payment_asiapay/models/payment_transaction.py:0
msgid "Received data with missing reference %(ref)s."
msgstr "Отримані дані з відсутнім референсом %(ref)s."

#. module: payment_asiapay
#. odoo-python
#: code:addons/payment_asiapay/models/payment_transaction.py:0
msgid "Received data with missing success code."
msgstr "Отримані дані з відсутнім кодом доступу."

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__asiapay_secure_hash_function__sha1
msgid "SHA1"
msgstr "SHA1"

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__asiapay_secure_hash_function__sha256
msgid "SHA256"
msgstr "SHA256"

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__asiapay_secure_hash_function__sha512
msgid "SHA512"
msgstr "SHA512"

#. module: payment_asiapay
#: model_terms:ir.ui.view,arch_db:payment_asiapay.payment_provider_form
msgid "Secure Hash Function"
msgstr "Функція хешу безпеки"

#. module: payment_asiapay
#: model_terms:ir.ui.view,arch_db:payment_asiapay.payment_provider_form
msgid "Secure Hash Secret"
msgstr "Секрет хешу безпеки"

#. module: payment_asiapay
#: model:ir.model.fields.selection,name:payment_asiapay.selection__payment_provider__asiapay_brand__siampay
msgid "SiamPay"
msgstr "SiamPay"

#. module: payment_asiapay
#: model:ir.model.fields,help:payment_asiapay.field_payment_provider__asiapay_merchant_id
msgid "The Merchant ID solely used to identify your AsiaPay account."
msgstr ""
"Ідентифікатор продавця, який використовується виключно для ідентифікації "
"вашого облікового запису AsiaPay."

#. module: payment_asiapay
#: model:ir.model.fields,help:payment_asiapay.field_payment_provider__asiapay_brand
msgid "The brand associated to your AsiaPay account."
msgstr "Бренд, пов’язаний з вашим обліковим записом AsiaPay."

#. module: payment_asiapay
#: model:ir.model.fields,help:payment_asiapay.field_payment_provider__asiapay_secure_hash_function
msgid "The secure hash function associated to your AsiaPay account."
msgstr "Захищена хеш-функція, пов’язана з вашим обліковим записом AsiaPay."

#. module: payment_asiapay
#: model:ir.model.fields,help:payment_asiapay.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Технічний код цього провайдера платежу."

#. module: payment_asiapay
#. odoo-python
#: code:addons/payment_asiapay/models/payment_transaction.py:0
msgid "Unknown success code: %s"
msgstr "Невідомий код доступу: %s"
