<?xml version="1.0"?>
<odoo><data>

    <!-- EVENT.REGISTRATION VIEWS -->
    <record model="ir.ui.view" id="view_event_registration_tree">
        <field name="name">event.registration.list</field>
        <field name="model">event.registration</field>
        <field name="arch" type="xml">
            <list string="Registration" multi_edit="1" sample="1"
                  expand="1" default_order="create_date desc"
                  class="o_event_registration_view_tree" js_class="registration_summary_dialog_list">
                <field name="barcode" column_invisible="True"/>
                <field name="active" column_invisible="True"/>
                <field name="create_date" optional="show" string="Registration Date"/>
                <field name="name"/>
                <field name="partner_id" optional="hide"/>
                <field name="email" optional="show"/>
                <field name="phone" optional="show"/>
                <field name="company_name" optional="hide"/>
                <field name="event_id" column_invisible="context.get('default_event_id')" readonly="state != 'draft'"/>
                <field name="event_ticket_id" domain="[('event_id', '=', event_id)]" readonly="state != 'draft'"/>
                <field name="activity_ids" widget="list_activity"/>
                <field name="state" decoration-info="state in ('draft', 'open')"
                       decoration-success="state == 'done'"
                       decoration-muted="state == 'cancel'" widget="badge"/>
                <field name="company_id" groups="base.group_multi_company" optional="hide" readonly="state != 'draft'"/>
                <field name="message_needaction" column_invisible="True"/>
                <button name="action_confirm" string="Registered" type="object" icon="fa-check"
                        invisible="not active or state != 'draft'"/>
                <button name="action_set_done" string="Mark as Attending" type="object" icon="fa-level-down"
                        invisible="not active or state != 'open'"/>
                <button name="action_cancel" string="Cancel" type="object"
                        class="o_btn_cancel_registration" icon="fa-times"
                        invisible="not active or (state != 'open' and state != 'draft')"/>
                <field name="activity_exception_decoration" widget="activity_exception"/>
            </list>
        </field>
    </record>

    <record model="ir.ui.view" id="view_event_registration_form">
        <field name="name">event.registration.form</field>
        <field name="model">event.registration</field>
        <field name="arch" type="xml">
            <form string="Event Registration">
                <field name="active" invisible="1"/>
                <header>
                    <button name="action_send_badge_email" string="Send by Email" type="object" class="oe_highlight"
                            invisible="not active or state != 'open' and state != 'done'"/>
                    <button name="action_confirm" string="Registered" type="object" class="oe_highlight"
                            invisible="not active or state != 'draft'"/>
                    <button name="action_set_done" string="Attended" type="object" class="oe_highlight"
                            invisible="not active or state != 'open'"/>
                    <button name="action_cancel" string="Cancel Registration" type="object"
                            invisible="not active or state == 'cancel'"/>
                    <field name="state" nolabel="1" colspan="2" widget="statusbar" statusbar_visible="open,done"
                            readonly="False" options="{'clickable': '1'}"/>
                </header>
                <sheet string="Registration">
                    <div class="oe_button_box" name="button_box"/>
                    <widget name="web_ribbon" text="Archived" bg_color="text-bg-danger" invisible="active"/>
                    <group>
                        <group string="Attendee" name="attendee">
                            <field class="o_text_overflow" name="name"/>
                            <field name="email"/>
                            <field name="phone" class="o_force_ltr" widget="phone" options="{'enable_sms': true}"/>
                            <field name="company_name" placeholder='e.g. "Azure Interior"'/>
                        </group>
                        <group string="Event Information" name="event">
                            <field class="text-break" name="event_id" readonly="event_id and state != 'draft' and id"
                                   context="{'name_with_seats_availability': True}" options="{'no_create': True}"/>
                            <field name="barcode" groups="base.group_no_one"/>
                            <field name="event_ticket_id" invisible="not event_id" readonly="event_ticket_id and state != 'draft' and id"
                                   context="{'name_with_seats_availability': True}" options="{'no_open': True, 'no_create': True}"
                                   domain="[('event_id', '=', event_id)]"/>
                            <field name="partner_id"/>
                            <field name="create_date" string="Registration Date" groups="base.group_no_one"/>
                            <field name="date_closed" groups="base.group_no_one"/>
                        </group>
                        <group string="Marketing" name="utm_link" groups="base.group_no_one">
                            <field name="utm_campaign_id"/>
                            <field name="utm_medium_id"/>
                            <field name="utm_source_id"/>
                        </group>
                    </group>
                    <field name="registration_properties" columns="2"/>
                    <notebook>
                        <page string="Questions" name="questions">
                            <field name="registration_answer_ids" widget="one2many">
                                <list editable="bottom">
                                    <field name="event_id" column_invisible="True" />
                                    <field name="question_id" domain="[('event_id', '=', event_id)]" options="{'no_create': True}" />
                                    <field name="question_type" string="Type" />
                                    <field name="value_answer_id"
                                        invisible="question_type != 'simple_choice'"
                                        domain="[('question_id', '=', question_id)]" options="{'no_create': True}"/>
                                    <field name="value_text_box" invisible="question_type == 'simple_choice'" />
                                </list>
                                <kanban class="o_kanban_mobile" create="false" delete="false">
                                    <field name="event_id"/>
                                    <field name="question_type"/>
                                    <templates>
                                        <t t-name="card" class="justify-content-between">
                                            <field class="fw-bold fs-5" name="question_id" domain="[('event_id', '=', event_id)]"/>
                                            <field name="value_answer_id"
                                                invisible="question_type != 'simple_choice'"
                                                domain="[('question_id', '=', question_id)]" options="{'no_create': True}"/>
                                            <field name="value_text_box"
                                                invisible="question_type == 'simple_choice'"/>
                                        </t>
                                    </templates>
                                </kanban>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter reload_on_post="True"/>
            </form>
        </field>
    </record>

    <record id="event_registration_view_kanban" model="ir.ui.view">
        <field name="name">event.registration.kanban</field>
        <field name="model">event.registration</field>
        <field name="priority">10</field>
        <field name="arch" type="xml">
            <kanban class="o_event_attendee_kanban_view" default_order="name, create_date desc" sample="1" js_class="registration_summary_dialog_kanban">
                <field name="name"/>
                <field name="state"/>
                <field name="active"/>
                <field name="barcode"/>
                <templates>
                    <t t-name="event_attendees_kanban_icons_desktop">
                        <!-- Will be removed in master -->
                        <div class="d-none d-md-block h-100">
                            <div id="event_attendees_kanban_icons_desktop" class="h-100 float-end p-2 d-flex align-items-end flex-column gap-1">
                                <t t-if="record.active.raw_value">
                                    <a class="btn btn-md btn-secondary" string="Confirm Attendance" name="action_set_done" type="object" invisible="state == 'done'"
                                        role="button" aria-label="Confirm Attendance Button" title="Confirm Attendance">
                                        <i class="fa fa-check" role="img"/>
                                    </a>
                                    <a class="btn btn-md btn-success" string="Reset To Registered" name="action_confirm" type="object" invisible="state != 'done'"
                                        role="button" aria-label="Reset To Registered Button" title="Reset To Registered">
                                        <i class="fa fa-check" role="img"/>
                                    </a>
                                </t>
                            </div>
                        </div>
                    </t>
                    <t t-name="event_attendees_kanban_icons_mobile">
                        <!-- Will be removed in master -->
                        <div id="event_attendees_kanban_icons_mobile" class="d-md-none d-flex align-items-end flex-column gap-1 h-100 ps-4">
                            <t t-if="record.active.raw_value">
                                <a class="btn btn-secondary d-flex justify-content-center align-items-center h-100 w-100"
                                    string="Confirm Attendance" name="action_set_done" type="object" invisible="state == 'done'" role="button">
                                    <i class="fa fa-check fa-3x" role="img" aria-label="Confirm Attendance Button" title="Confirm Attendance"/>
                                </a>
                                <a class="btn btn-success d-flex justify-content-center align-items-center h-100 w-100"
                                    string="Reset To Registered" name="action_confirm" type="object" invisible="state != 'done'" role="button">
                                    <i class="fa fa-check fa-3x" role="img" aria-label="Reset To Registered Button" title="Reset To Registered"/>
                                </a>
                            </t>
                        </div>
                    </t>
                    <t t-name="card" class="row g-0">
                        <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                        <div class="col-8 col-md-9">
                            <field class="d-block fw-bold fs-5" name="name"/>
                            <field name="state" widget="badge" decoration-success="state == 'done'" class="position-absolute top-0 end-0 o_event_registration_kanban_badge"/>
                            <div class="o_kanban_event_registration_event_name">
                                <field class="text-truncate text-primary" name="event_id" invisible="context.get('default_event_id')"/>
                            </div>
                            <span class="text-truncate" invisible="not company_name">
                                <i class="fa fa-building" title="Attendee Company"/> <field name="company_name"/>
                            </span>
                            <div id="event_ticket_id">
                                <field name="registration_properties"/>
                                <t t-if="record.event_ticket_id.raw_value">
                                    <i class="fa fa-ticket" title="Ticket type"/>
                                    <field name="event_ticket_id" class="fw-bold text-truncate ms-1"/>
                                </t>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="view_event_registration_calendar" model="ir.ui.view">
        <field name="name">event.registration.calendar</field>
        <field name="model">event.registration</field>
        <field eval="2" name="priority"/>
        <field name="arch" type="xml">
            <calendar date_start="event_begin_date" date_stop="event_end_date" string="Event Registration" color="event_id" event_limit="5">
                <field name="event_id" filters="1"/>
                <field name="name"/>
                <field name="registration_properties"/>
            </calendar>
        </field>
    </record>

    <record model="ir.ui.view" id="view_event_registration_pivot">
        <field name="name">event.registration.pivot</field>
        <field name="model">event.registration</field>
        <field name="arch" type="xml">
            <pivot string="Registration" display_quantity="1" sample="1">
                <field name="event_id" type="row"/>
            </pivot>
        </field>
    </record>

    <record model="ir.ui.view" id="view_event_registration_graph">
        <field name="name">event.registration.graph</field>
        <field name="model">event.registration</field>
        <field name="arch" type="xml">
            <graph string="Registration" sample="1">
                <field name="event_id"/>
            </graph>
        </field>
    </record>

    <record model="ir.ui.view" id="view_registration_search">
        <field name="name">event.registration.search</field>
        <field name="model">event.registration</field>
        <field name="arch" type="xml">
            <search string="Event Registration">
                <field name="id" string="Registration ID"/>
                <field name="name" string="Participant" filter_domain="['|', '|', ('name', 'ilike', self), ('email', 'ilike', self), ('company_name', 'ilike', self)]"/>
                <field name="company_id"/>
                <field name="partner_id"/>
                <field name="event_ticket_id" string="Ticket"/>
                <field name="event_id"/>
                <field name="event_user_id" string="Responsible" invisible="1"/>
                <field name="event_organizer_id" string="Organizer" invisible="1"/>
                <filter string="Ongoing Events" name="filter_is_ongoing" domain="[('event_id.is_ongoing', '=', True)]"/>
                <filter string="Taken" name="taken" domain="[('state', 'in', ['open', 'done'])]"/>
                <separator/>
                <filter string="Unconfirmed" name="unconfirmed" domain="[('state', '=', 'draft')]"/>
                <filter string="Registered" name="confirmed" domain="[('state', '=', 'open')]"/>
                <filter string="Attended" name="attended" domain="[('state', '=', 'done')]"/>
                <separator/>
                <filter string="Registration Date" name="filter_create_date" date="create_date"/>
                <filter string="Event Start Date" name="filter_event_begin_date" date="event_begin_date"/>
                <filter string="Attended Date" name="filter_date_closed" date="date_closed"/>
                <separator/>
                <filter invisible="1" string="Late Activities" name="activities_overdue"
                    domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                    help="Show all records which has next action date is before today"/>
                <filter invisible="1" string="Today Activities" name="activities_today"
                    domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                    domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                <filter string="Last 30 days" name="filter_last_month_creation" domain="[('create_date','&gt;', (context_today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"/>
                <separator/>
                <filter string="Archived" name="filter_inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Partner" name="partner" domain="[]" context="{'group_by':'partner_id'}"/>
                    <filter string="Event" name="group_event" domain="[]" context="{'group_by':'event_id'}"/>
                    <filter string="Ticket Type" name ="group_event_ticket_id" domain="[]" context="{'group_by': 'event_ticket_id'}"/>
                    <filter string="Status" name="status" domain="[]" context="{'group_by':'state'}"/>
                    <filter string="Registration Date" name="group_by_create_date_week" domain="[]" context="{'group_by': 'create_date:week'}"
                            invisible="context.get('registration_view_hide_group_by_create_date_week')"/>
                    <filter string="Registration Date" name="group_by_create_date_day" domain="[]" context="{'group_by': 'create_date:day'}"
                            invisible="not context.get('registration_view_hide_group_by_create_date_week')"/>
                    <filter string="Campaign" name="group_by_utm_campaign_id" domain="[]"
                            context="{'group_by': 'utm_campaign_id'}"/>
                    <filter string="Medium" name="group_by_utm_medium_id" domain="[]"
                            context="{'group_by': 'utm_medium_id'}"/>
                    <filter string="Source" name="group_by_utm_source_id" domain="[]"
                            context="{'group_by': 'utm_source_id'}"/>
               </group>
            </search>
        </field>
    </record>

    <!-- Search view typically used when coming from a specific event, meaning we don't need event-related fields -->
    <record id="event_registration_view_search_event_specific" model="ir.ui.view">
        <field name="name">event.registration.view.search.event.specific</field>
        <field name="model">event.registration</field>
        <field name="inherit_id" ref="view_registration_search"/>
        <field name="mode">primary</field>
        <field name="priority">32</field>
        <field name="arch" type="xml">
            <xpath expr="//search/field[@name='event_id']" position="replace"/>
            <xpath expr="//search/filter[@name='filter_is_ongoing']" position="replace"/>
            <xpath expr="//search/group/filter[@name='group_event']" position="replace"/>
        </field>
    </record>

    <!-- EVENT.REGISTRATION ACTIONS -->
    <record id="act_event_registration_from_event" model="ir.actions.act_window">
        <field name="res_model">event.registration</field>
        <field name="name">Attendees</field>
        <field name="path">attendees</field>
        <field name="view_mode">list,kanban,form,calendar,graph</field>
        <field name="domain">[('event_id', '=', active_id)]</field>
        <field name="context">{
            'default_event_id': active_id,
            'name_with_seats_availability': True,
            'search_default_taken': True,
        }</field>
        <field name="search_view_id" ref="event_registration_view_search_event_specific"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Attendees yet!
            </p><p>
                Wait until Attendees register to your Event or create their registrations manually.
            </p>
        </field>
    </record>

    <!-- We need kanban view to be displayed first while coming from registration desk, so we have created
     new action and changed the view sequence. -->
    <record id="event_registration_action_kanban" model="ir.actions.act_window">
        <field name="res_model">event.registration</field>
        <field name="name">Attendees</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="domain">[('event_id', '=', active_id)]</field>
        <field name="context">{'default_event_id': active_id, 'is_registration_desk_view': True}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Attendees yet!
            </p><p>
                Wait until Attendees register to your Event or create their registrations manually.
            </p>
        </field>
    </record>

    <record id="event_registration_action" model="ir.actions.act_window">
        <field name="res_model">event.registration</field>
        <field name="name">Attendees</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{'search_default_filter_is_ongoing': True, 'is_registration_desk_view': True}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Attendees expected yet!
            </p><p>
                Wait until Attendees register to your Event or create their registrations manually.
            </p>
        </field>
    </record>

    <record id="event_registration_action_tree" model="ir.actions.act_window">
       <field name="name">Event registrations</field>
       <field name="res_model">event.registration</field>
       <field name="view_mode">list,kanban,form,calendar,graph</field>
    </record>

    <record id="action_registration" model="ir.actions.act_window">
        <field name="name">Attendees</field>
        <field name="res_model">event.registration</field>
        <field name="domain"></field>
        <field name="view_mode">graph,pivot,kanban,list,form</field>
        <field name="context">{
                'search_default_filter_last_month_creation': 1,
                'search_default_taken': 1,
                'search_default_status': 2,
                'search_default_group_by_create_date_day': 3,
                'search_default_group_event': 1,
                'registration_view_hide_group_by_create_date_week': 1,
            }
        </field>
        <field name="search_view_id" ref="view_registration_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Attendees yet!
            </p><p>
                From this dashboard you can report, analyze and detect trends regarding your event registrations.
            </p>
        </field>
    </record>

    <record id="event_registration_action_stats_from_event" model="ir.actions.act_window">
        <field name="name">Registration statistics</field>
        <field name="res_model">event.registration</field>
        <field name="view_mode">graph,pivot,kanban,list,form</field>
        <field name="domain">[('event_id', '=', active_id)]</field>
        <field name="context">{
                'default_event_id': active_id,
                'search_default_group_by_create_date_day': 1,
                'search_default_status': 2,
                'registration_view_hide_group_by_create_date_week': 1,
            }
        </field>
        <field name="search_view_id" ref="event_registration_view_search_event_specific"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Attendees yet!
            </p><p>
                From this dashboard you can report, analyze and detect trends regarding your event registrations.
            </p>
        </field>
    </record>

    <menuitem name="Attendees"
        id="menu_action_registration"
        parent="event.menu_reporting_events"
        sequence="4"
        action="action_registration"
        groups="event.group_event_user"/>
</data></odoo>
