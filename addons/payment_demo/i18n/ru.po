# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_demo
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_details
msgid ""
"<select id=\"simulated_payment_state\" class=\"form-select\">\n"
"                    <option value=\"done\" title=\"Successful payment\">\n"
"                        Successful\n"
"                    </option>\n"
"                    <option value=\"pending\" title=\"Payment processing\">\n"
"                        Pending\n"
"                    </option>\n"
"                    <option value=\"cancel\" title=\"Payment cancelled by customer\">\n"
"                        Cancelled\n"
"                    </option>\n"
"                    <option value=\"error\" title=\"Processing error\">\n"
"                        Error\n"
"                    </option>\n"
"                </select>"
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_inline_form
msgid "<small><b>City</b></small>"
msgstr "<small><b>Город</b></small>"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_inline_form
msgid "<small><b>Country</b></small>"
msgstr "<small><b>Страна</b></small>"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_inline_form
msgid "<small><b>Name</b></small>"
msgstr "<small><b>Имя</b></small>"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_inline_form
msgid "<small><b>Street and Number</b></small>"
msgstr "<small><b>Улица и номер</b></small>"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_inline_form
msgid "<small><b>Zip Code</b></small>"
msgstr "<small><b>Почтовый индекс</b></small>"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_inline_form
msgid "<small>Email</small>"
msgstr "<small>Электронная почта</small>"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_details
msgid "<small>Payment Details (test data)</small>"
msgstr "<small>Платежные реквизиты (тестовые данные)</small>"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_details
msgid "<small>Payment Status</small>"
msgstr "<small>Статус платежа</small>"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_inline_form
msgid "<small>Street 2</small>"
msgstr "<small>Улица 2</small>"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_transaction_form
msgid "Authorize"
msgstr "Авторизация"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_transaction_form
msgid "Cancel"
msgstr "Отменить"

#. module: payment_demo
#: model:ir.model.fields.selection,name:payment_demo.selection__payment_token__demo_simulated_state__cancel
msgid "Canceled"
msgstr "Отменено"

#. module: payment_demo
#: model:ir.model.fields,field_description:payment_demo.field_payment_transaction__capture_manually
msgid "Capture Amount Manually"
msgstr "Захватить сумму вручную"

#. module: payment_demo
#: model:ir.model.fields,help:payment_demo.field_payment_transaction__capture_manually
msgid ""
"Capture the amount from Odoo, when the delivery is completed.\n"
"Use this if you want to charge your customers cards only when\n"
"you are sure you can ship the goods to them."
msgstr ""
"Получите сумму из Odoo, когда доставка будет завершена.\n"
"Используйте этот способ, если хотите снимать деньги с карт клиентов только тогда\n"
"вы уверены, что можете отправить им товар."

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_checkout_form
msgid "Close"
msgstr "Закрыть"

#. module: payment_demo
#: model:ir.model.fields,field_description:payment_demo.field_payment_provider__code
msgid "Code"
msgstr "Код"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_transaction_form
msgid "Confirm"
msgstr "Подтвердить"

#. module: payment_demo
#: model:ir.model.fields.selection,name:payment_demo.selection__payment_token__demo_simulated_state__done
msgid "Confirmed"
msgstr "Подтверждена"

#. module: payment_demo
#: model:ir.model.fields.selection,name:payment_demo.selection__payment_provider__code__demo
#: model:payment.method,name:payment_demo.payment_method_demo
msgid "Demo"
msgstr "Демо"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_checkout_form
msgid "Demo Express Checkout"
msgstr "Демонстрационная экспресс-касса"

#. module: payment_demo
#. odoo-python
#: code:addons/payment_demo/models/payment_provider.py:0
msgid "Demo providers should never be enabled."
msgstr "Демонстрационные провайдеры никогда не должны быть включены."

#. module: payment_demo
#: model:ir.model.fields.selection,name:payment_demo.selection__payment_token__demo_simulated_state__error
msgid "Error"
msgstr "Ошибка"

#. module: payment_demo
#. odoo-javascript
#: code:addons/payment_demo/static/src/js/express_checkout_form.js:0
msgid "No delivery method is available."
msgstr "Нет доступных методов доставки."

#. module: payment_demo
#. odoo-python
#: code:addons/payment_demo/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Не найдено ни одной транзакции, соответствующей ссылке %s."

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_checkout_form
msgid "Pay"
msgstr "Оплатить"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_checkout_form
msgid "Pay with Demo"
msgstr "Оплата с помощью демо-версии"

#. module: payment_demo
#: model:ir.model,name:payment_demo.model_payment_provider
msgid "Payment Provider"
msgstr "Поставщик платежей"

#. module: payment_demo
#: model:ir.model,name:payment_demo.model_payment_token
msgid "Payment Token"
msgstr "Платежный токен"

#. module: payment_demo
#: model:ir.model,name:payment_demo.model_payment_transaction
msgid "Payment Transaction"
msgstr "Платеж"

#. module: payment_demo
#. odoo-javascript
#: code:addons/payment_demo/static/src/js/payment_demo_mixin.js:0
msgid "Payment processing failed"
msgstr "Обработка платежа не удалась"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.token_inline_form
msgid "Payments made with this payment method will be <b>successful</b>."
msgstr ""
"Платежи, произведенные с помощью этого способа оплаты, будут "
"<b>успешными</b>."

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.token_inline_form
msgid ""
"Payments made with this payment method will be automatically "
"<b>cancelled</b>."
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.token_inline_form
msgid "Payments made with this payment method will remain <b>pending</b>."
msgstr ""
"Платежи, произведенные с помощью этого способа оплаты, останутся "
"<b>незавершенными</b>."

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.token_inline_form
msgid ""
"Payments made with this payment method will simulate a processing "
"<b>error</b>."
msgstr ""
"Платежи, произведенные с помощью этого метода оплаты, будут имитировать "
"<b>ошибку</b> обработки."

#. module: payment_demo
#: model:ir.model.fields.selection,name:payment_demo.selection__payment_token__demo_simulated_state__pending
msgid "Pending"
msgstr "В ожидании"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_transaction_form
msgid "Set to Error"
msgstr "Установить на ошибку"

#. module: payment_demo
#: model:ir.model.fields,field_description:payment_demo.field_payment_token__demo_simulated_state
msgid "Simulated State"
msgstr "Моделируемое состояние"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_checkout_form
msgid "Test Mode"
msgstr "Тестовый режим"

#. module: payment_demo
#: model:ir.model.fields,help:payment_demo.field_payment_token__demo_simulated_state
msgid "The state in which transactions created from this token should be set."
msgstr ""
"Состояние, в котором должны быть установлены транзакции, созданные с помощью"
" этого токена."

#. module: payment_demo
#: model:ir.model.fields,help:payment_demo.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Технический код данного провайдера платежей."

#. module: payment_demo
#. odoo-python
#: code:addons/payment_demo/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr "Транзакция не привязана к токену."

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_checkout_form
msgid "Unpublished"
msgstr "Неопубликованный"

#. module: payment_demo
#. odoo-javascript
#: code:addons/payment_demo/static/src/js/express_checkout_form.js:0
msgid "Validation Error"
msgstr "Ошибка проверки"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_details
msgid "XXXX XXXX XXXX XXXX"
msgstr "XXXX XXXX XXXX XXXX"

#. module: payment_demo
#. odoo-python
#: code:addons/payment_demo/models/payment_transaction.py:0
msgid "You selected the following demo payment status: %s"
msgstr "Вы выбрали следующий статус демо-платежа: %s"
