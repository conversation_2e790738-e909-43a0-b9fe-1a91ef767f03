# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_stripe
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
msgid "Cannot display the payment form"
msgstr "Impossible d'afficher le formulaire de paiement"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__code
msgid "Code"
msgstr "Code"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Connect Stripe"
msgstr "Connecter Stripe"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr "Impossible d'établir la connexion avec l'API."

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/express_checkout_form.js:0
msgid "Delivery"
msgstr "Livraison"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Enable Apple Pay"
msgstr "Activer Apple Pay"

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/express_checkout_form.js:0
msgid "Free Shipping"
msgstr "Expédition gratuite"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Generate your webhook"
msgstr "Générer votre webhook"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Get your Secret and Publishable keys"
msgstr "Obtenir vos clés secrètes et publiables"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_provider__stripe_webhook_secret
msgid ""
"If a webhook is enabled on your Stripe account, this signing secret must be "
"set to authenticate the messages sent from Stripe to Odoo."
msgstr ""
"Si un webhook est activé sur votre compte Stripe, ce secret de signature "
"doit être défini pour authentifier les messages envoyés de Stripe à Odoo."

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
msgid "Incorrect payment details"
msgstr "Données de paiement incorrectes"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Aucune transaction ne correspond à la référence %s."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
msgid "Other Payment Providers"
msgstr "Autres fournisseurs de paiement"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_provider
msgid "Payment Provider"
msgstr "Fournisseur de paiement"

#. module: payment_stripe
#: model:ir.actions.act_window,name:payment_stripe.action_payment_provider_onboarding
msgid "Payment Providers"
msgstr "Fournisseurs de paiement"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_token
msgid "Payment Token"
msgstr "Jeton de paiement"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transaction"

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr "Échec du traitement du paiement"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
msgid "Please use live credentials to enable Apple Pay."
msgstr "Veuillez utiliser des identifiants live pour activer Apple Pay."

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__stripe_publishable_key
msgid "Publishable Key"
msgstr "Clé publiable"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
msgid "Received data with invalid intent status: %s"
msgstr "Réception de données avec un statut d'intention invalide : %s"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
msgid "Received data with missing intent status."
msgstr "Données reçues avec statut d'intention manquant."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
msgid "Received data with missing merchant reference"
msgstr "Données reçues avec référence marchand manquant"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__stripe_secret_key
msgid "Secret Key"
msgstr "Clé secrète"

#. module: payment_stripe
#: model:ir.model.fields.selection,name:payment_stripe.selection__payment_provider__code__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
msgid ""
"Stripe Connect is not available in your country, please use another payment "
"provider."
msgstr ""
"Stripe Connect n'est pas disponible dans votre pays. Veuillez utiliser un "
"autre fournisseur de paiement."

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_mandate
msgid "Stripe Mandate"
msgstr "Mandat Stripe"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_payment_method
msgid "Stripe Payment Method ID"
msgstr "ID du mode de paiement Stripe"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
msgid "Stripe Proxy error: %(error)s"
msgstr "Erreur de Stripe Proxy : %(error)s"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
msgid "Stripe Proxy: An error occurred when communicating with the proxy."
msgstr ""
"Stripe Proxy : Une erreur est survenue lors de la communication avec le "
"proxy."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
msgid "Stripe Proxy: Could not establish the connection."
msgstr "Proxy Stripe : Impossible d'établir la connexion."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#: code:addons/payment_stripe/models/payment_transaction.py:0
msgid ""
"The communication with the API failed.\n"
"Stripe gave us the following info about the problem:\n"
"'%s'"
msgstr ""
"Échec de la communication avec l'API.\n"
"Stripe nous a fourni les informations suivantes sur le problème :\n"
"'%s'"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
msgid "The customer left the payment page."
msgstr "Le client a quitté la page de paiement."

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_provider__stripe_publishable_key
msgid "The key solely used to identify the account with Stripe"
msgstr "La clé uniquement utilisée pour identifier le compte avec Stripe"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
msgid ""
"The refund did not go through. Please log into your Stripe Dashboard to get "
"more information on that matter, and address any accounting discrepancies."
msgstr ""
"Le remboursement n'a pas été effectué. Veuillez vous connecter à votre "
"tableau de bord Stripe pour obtenir plus d'informations à ce sujet et "
"corriger les éventuelles anomalies comptables."

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Le code technique de ce fournisseur de paiement."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr "La transaction n'est pas liée à un jeton."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_token.py:0
msgid "Unable to convert payment token to new API."
msgstr "Impossible de convertir le jeton de paiement en nouvel API."

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__stripe_webhook_secret
msgid "Webhook Signing Secret"
msgstr "Secret de signature webhook"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
msgid "You Stripe Webhook was successfully set up!"
msgstr "Votre webhook Stripe a été configuré avec succès !"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
msgid ""
"You cannot create a Stripe Webhook if your Stripe Secret Key is not set."
msgstr ""
"Vous ne pouvez pas créer un webhook Stripe si votre Clé secrète Stripe n'est"
" pas définie."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
msgid ""
"You cannot set the provider state to Enabled until your onboarding to Stripe"
" is completed."
msgstr ""
"Vous ne pouvez pas définir le statut du fournisseur sur Activé tant que vous"
" n'avez pas complété le parcours d'intégration à Stripe."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
msgid ""
"You cannot set the provider to Test Mode while it is linked with your Stripe"
" account."
msgstr ""
"Vous ne pouvez pas définir le fournisseur en Mode test tant qu'il est lié à "
"votre compte Stripe."

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
msgid "Your Stripe Webhook is already set up."
msgstr "Votre webhook Stripe est déjà configuré."

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/express_checkout_form.js:0
msgid "Your order"
msgstr "Votre commande "

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
msgid "Your web domain was successfully verified."
msgstr "Votre domaine web a été vérifié avec succès."
