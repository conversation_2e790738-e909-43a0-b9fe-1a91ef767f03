<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="follow">
        <div t-attf-class="input-group js_follow #{div_class}" t-att-data-id="object.id"
                  t-att-data-object="object._name"
                  t-att-data-follow="object.id and object.message_is_follower and 'on' or 'off'"
                  t-att-data-unsubscribe="'unsubscribe' if 'unsubscribe' in request.params else None">
            <input
                  type="email" name="email"
                  class="js_follow_email form-control"
                  placeholder="your email..."
                  groups="base.group_public"/>
            <div t-if="icons_design and not request.env.user._is_public()" class="js_follow_icons_container">
                <button class="btn text-reset js_unfollow_btn">
                    <div class="d-flex align-items-center">
                        <small>Unfollow</small><i class="fa fa-fw ms-1"/>
                    </div>
                </button>
                <button class="btn text-reset js_follow_btn">
                    <div class="d-flex align-items-center">
                        <small>Follow</small><i class="fa fa-fw ms-1"/>
                    </div>
                </button>
            </div>
            <t t-else="">
                <button href="#" t-attf-class="btn btn-secondary js_unfollow_btn">Unsubscribe</button>
                <button href="#" t-attf-class="btn btn-primary js_follow_btn">Subscribe</button>
            </t>
        </div>
    </template>
</odoo>
