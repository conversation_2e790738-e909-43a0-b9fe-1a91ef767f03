# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_mail
# 
# Translators:
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:04+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "<small>Follow</small><i class=\"fa fa-fw ms-1\"/>"
msgstr "<small>Підписатися</small><i class=\"fa fa-fw ms-1\"/>"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "<small>Unfollow</small><i class=\"fa fa-fw ms-1\"/>"
msgstr "<small>Відписатися</small><i class=\"fa fa-fw ms-1\"/>"

#. module: website_mail
#. odoo-javascript
#: code:addons/website_mail/static/src/js/follow.js:0
msgid "Error"
msgstr "Помилка"

#. module: website_mail
#: model:ir.model,name:website_mail.model_ir_http
msgid "HTTP Routing"
msgstr "Маршрутизація HTTP"

#. module: website_mail
#: model:ir.model,name:website_mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "Гарантійний договір видавця"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "Subscribe"
msgstr "Підписатися"

#. module: website_mail
#. odoo-python
#: code:addons/website_mail/controllers/main.py:0
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Google reCaptcha виявила підозрілу активність."

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "Unsubscribe"
msgstr "Відписатися"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "your email..."
msgstr "ваш email..."
