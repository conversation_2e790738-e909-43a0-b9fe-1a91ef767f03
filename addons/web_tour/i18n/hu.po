# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_tour
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# krnkris, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: krnkris, 2024\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "(recording keyboard)"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "(run:"
msgstr ""

#. module: web_tour
#: model:ir.model.constraint,message:web_tour.constraint_web_tour_tour_uniq_name
msgid "A tour already exists with this name . Tour's name must be unique!"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_utils.js:0
msgid "Click the top left corner to navigate across apps."
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__content
msgid "Content"
msgstr "Tartalom"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__create_uid
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__create_uid
msgid "Created by"
msgstr "Létrehozta"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__create_date
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__custom
msgid "Custom"
msgstr "Egyéni"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.js:0
msgid "Custom tour '%s' couldn't be saved!"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.js:0
msgid "Custom tour '%s' has been added."
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__display_name
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__display_name
msgid "Display Name"
msgstr "Megjelenített név"

#. module: web_tour
#: model:ir.actions.server,name:web_tour.tour_export_js_action
msgid "Export JS"
msgstr ""

#. module: web_tour
#: model:ir.model,name:web_tour.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP irányítás"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__id
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__id
msgid "ID"
msgstr "ID"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__write_uid
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__write_date
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__write_date
msgid "Last Updated on"
msgstr "Frissítve"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.tour_list
msgid "Menu"
msgstr "Menü"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__name
msgid "Name"
msgstr "Név"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "Name:"
msgstr "Név:"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_service.js:0
#: code:addons/web_tour/static/src/widgets/tour_start.xml:0
#: model:ir.model.fields,field_description:web_tour.field_res_users__tour_enabled
msgid "Onboarding"
msgstr "Beléptetés"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__rainbow_man_message
msgid "Rainbow Man Message"
msgstr ""

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.tour_form
msgid "Rainbow Man Message..."
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
#: code:addons/web_tour/static/src/views/tour_controller.xml:0
msgid "Record"
msgstr "Bejegyzés"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/views/tour_controller.xml:0
msgid "Record Tour"
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__run
msgid "Run"
msgstr "Futtatás"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "Save"
msgstr "Mentés"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
msgid "Scroll down to reach the next step."
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
msgid "Scroll up to reach the next step."
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__sequence
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__sequence
msgid "Sequence"
msgstr "Sorszám"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__sharing_url
msgid "Sharing URL"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/widgets/tour_start.xml:0
msgid "Start Tour"
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__url
msgid "Starting URL"
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__step_ids
msgid "Step"
msgstr "Lépés"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
#: model_terms:ir.ui.view,arch_db:web_tour.tour_form
msgid "Steps"
msgstr "Lépések"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "Steps:"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/widgets/tour_start.xml:0
msgid "Test Tour"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/widgets/tour_start.xml:0
msgid "Testing"
msgstr "Tesztelés"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.tour_search
msgid "Tip"
msgstr "Tipp"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__tour_id
msgid "Tour"
msgstr ""

#. module: web_tour
#: model:ir.model,name:web_tour.model_web_tour_tour_step
msgid "Tour's step"
msgstr ""

#. module: web_tour
#: model:ir.actions.act_window,name:web_tour.tour_action
#: model:ir.model,name:web_tour.model_web_tour_tour
#: model:ir.ui.menu,name:web_tour.menu_tour_action
msgid "Tours"
msgstr "Bejárások"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__trigger
msgid "Trigger"
msgstr "Indítás"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "Url:"
msgstr ""

#. module: web_tour
#: model:ir.model,name:web_tour.model_res_users
msgid "User"
msgstr "Felhasználó"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__user_consumed_ids
msgid "User Consumed"
msgstr ""

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.tour_form
msgid "e.g. My_Tour"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "name_of_the_tour"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "trigger"
msgstr ""
