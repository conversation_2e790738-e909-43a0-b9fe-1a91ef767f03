<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Stock Portal Access Rules -->
        <record id="stock_picking_rule_portal" model="ir.rule">
            <field name="name">Portal Follower Transfers</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="domain_force">['|', '|', ('message_partner_ids', 'in', [user.partner_id.id]), ('partner_id', '=', user.partner_id.id), ('sale_id.partner_id', '=', user.partner_id.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
        </record>
    </data>
</odoo>
