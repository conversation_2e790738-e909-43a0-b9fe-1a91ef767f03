# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock
# 
# Translators:
# <PERSON>, 2025
# Wil <PERSON>do<PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid ""
".\n"
"                Manual actions may be needed."
msgstr ""
".\n"
"                可能需要手動操作."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Awaiting arrival"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> 等候到貨"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/>Preparation"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/>準備中"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/> Cancelled"
msgstr "<i class=\"fa fa-fw fa-times\"/> 已取消"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/>Cancelled"
msgstr "<i class=\"fa fa-fw fa-times\"/>已取消"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-truck\"/> Received"
msgstr "<i class=\"fa fa-fw fa-truck\"/> 已收貨"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">銷售</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid ""
"<span class=\"small badge rounded-pill text-bg-success orders_label_text_align\">\n"
"                                        <i class=\"fa fa-fw fa-truck\"/> Shipped\n"
"                                    </span>"
msgstr ""
"<span class=\"small badge rounded-pill text-bg-success orders_label_text_align\">\n"
"                                        <i class=\"fa fa-fw fa-truck\"/> 已發貨\n"
"                                    </span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_cancel_view_form_inherit
msgid ""
"<span invisible=\"not display_delivery_alert\">\n"
"                    Some deliveries are already done. Returns can be created from the Delivery Orders.\n"
"                </span>"
msgstr ""
"<span invisible=\"not display_delivery_alert\">\n"
"                    部份送貨已完成。可從送貨單建立退貨。\n"
"                </span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "<strong>Customer Reference</strong>"
msgstr "<strong>客戶參考</strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_saleorder_document_inherit_sale_stock
msgid "<strong>Incoterm</strong>"
msgstr "<strong>國際貿易術語</strong>"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__is_storable
msgid "A storable product is a product for which you manage stock."
msgstr "可儲存產品是需要你管理庫存水平的產品。"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"根據產品配置，可透過機制自動計算交貨數量：\n"
"  - 手動：在資料行手動設定數量\n"
"  - 從開支分析：數量是已過賬開支的數量總和\n"
"  - 工時表：連結至此銷售資料行的任務所記錄的工時總和\n"
"  - 庫存變動：採用已確認提貨的數量\n"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "All planned operations included"
msgstr "包括所有計劃的操作"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_rules_report__so_route_ids
msgid "Apply specific routes"
msgstr "使用特定路線"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__direct
msgid "As soon as possible"
msgstr "盡快"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Availability"
msgstr "可用"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Available"
msgstr "可用"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Available in stock"
msgstr "有存貨"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__delivery_status
msgid ""
"Blue: Not Delivered/Started\n"
"            Orange: Partially Delivered\n"
"            Green: Fully Delivered"
msgstr ""
"藍色：未送貨/未開始交付\n"
"            橙色：已部份交付\n"
"            綠色：已全數交付"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_stock_rules_report__so_route_ids
msgid "Choose to apply SO lines specific routes."
msgstr "選擇應用的訂單明細行特定路線."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_company
msgid "Companies"
msgstr "公司"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__effective_date
msgid "Completion date of the first delivery order."
msgstr "第一個交貨訂單的完成日期."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "Customer reference"
msgstr "客戶參考"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Date:"
msgstr "日期:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_users__property_warehouse_id
msgid "Default Warehouse"
msgstr "預設倉庫"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Delivery"
msgstr "送貨"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_cancel__display_delivery_alert
msgid "Delivery Alert"
msgstr "送貨提醒"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_count
msgid "Delivery Orders"
msgstr "交貨單"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_status
msgid "Delivery Status"
msgstr "送貨狀態"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"交貨日期，您可以向客戶承諾，在服務產品的情況下，從訂單行的最短提前期計算。在裝運的情況下，將考慮訂單的裝運策略，以使用訂單行的最短或最長提前期。"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__display_qty_widget
msgid "Display Qty Widget"
msgstr "顯示數量小部件"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
msgid ""
"Do not forget to change the partner on the following delivery orders: %s"
msgstr "不要忘記更改以下交貨訂單上的合作夥伴：%s"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__effective_date
msgid "Effective Date"
msgstr "實際日期"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "Exception(s) occurred on the picking:"
msgstr "揀貨出現異常："

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s) occurred on the sale order(s):"
msgstr "銷售訂單發生異常:"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s):"
msgstr "異常:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__expected_date
msgid "Expected Date"
msgstr "預計交貨日期"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Expected Delivery"
msgstr "預計送貨"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Expected:"
msgstr "預期："

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__forecast_expected_date
msgid "Forecast Expected Date"
msgstr "推測預計日期"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Forecasted Stock"
msgstr "預測庫存"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__free_qty_today
msgid "Free Qty Today"
msgstr "今日免費數量"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__full
msgid "Fully Delivered"
msgstr "全數完成送貨"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__show_json_popover
msgid "Has late picking"
msgstr "有延後揀貨"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr "如果您同時交付所有產品，發貨單將根據最大的產品交貨時間來計劃。否則，它將根據最短的交貨時間。"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Impacted Transfer(s):"
msgstr "受影響的調撥:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm
msgid "Incoterm"
msgstr "國際貿易術語"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm_location
msgid "Incoterm Location"
msgstr "國際貿易術語位置"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "Incoterm details"
msgstr "國際貿易術語詳情"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Incoterm:"
msgstr "國際貿易術語："

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__incoterm
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr "國際商業條款是一系列用於國際交易的事先定義的商業條款。"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_users_view_form
msgid "Inventory"
msgstr "庫存"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_route
msgid "Inventory Routes"
msgstr "庫存路線"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__is_mto
msgid "Is Mto"
msgstr "是特別訂製"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__json_popover
msgid "JSON data for the popover widget"
msgstr "彈出視窗小部件的 JSON 資料"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move
msgid "Journal Entry"
msgstr "日記賬記項"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move_line
msgid "Journal Item"
msgstr "日記賬項目"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Last Delivery Orders"
msgstr "最新送貨單"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr "前置時間"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_lot
msgid "Lot/Serial"
msgstr "批次/序列號"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Manual actions may be needed."
msgstr "可能需要手動操作."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__use_security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for delivery that many days earlier than the actual promised date, to cope "
"with unexpected delays in the supply chain."
msgstr "對顧客承諾的日期的誤差幅度。產品將安排比實際交貨日期提前幾天交貨，以應付供應鏈中意外的延誤。"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_company__security_lead
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__security_lead
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for procurement and delivery that many days earlier than the actual promised"
" date, to cope with unexpected delays in the supply chain."
msgstr "向客戶承諾日期的誤差幅度。產品將被比實際承諾的日期提前很多天安排採購和交貨，以應對供應鏈中的意外延誤。"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "更新數量的方法"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Move forward expected delivery dates by"
msgstr "將預期交貨日期提前"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_stock_help_message_template
msgid "No delivery yet! Automate them with sales orders."
msgstr "還沒出貨！利用銷售訂單，實現自動化送貨。"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "No future availability"
msgstr "沒有未來貨量"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__pending
msgid "Not Delivered"
msgstr "未送貨"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Not enough future availability"
msgstr "未來貨量不足"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr "訂單被確認和產品交付給客戶之間的天數"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "On"
msgstr "開"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__partial
msgid "Partially Delivered"
msgstr "部份完成送貨"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__default_picking_policy
msgid "Picking Policy"
msgstr "揀貨策略"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_procurement_group
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__procurement_group_id
msgid "Procurement Group"
msgstr "補貨組"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_product_template
msgid "Product"
msgstr "商品"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "產品移動(移庫明細)"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_available_today
msgid "Qty Available Today"
msgstr "今日可用數量"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_to_deliver
msgid "Qty To Deliver"
msgstr "要交付的數量"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/sale_stock_forecasted/forecasted_details.xml:0
msgid "Quotations"
msgstr "報價單"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "RETURN"
msgstr "【退貨】"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_stock_help_message_template
msgid ""
"Reduce back orders with reservations, locations management, smart\n"
"                replenishment propositions, shipping connectors, barcode scanner, etc."
msgstr ""
"利用預留功能、位置管理、智慧補貨建議、\n"
"                運輸連接器、條碼掃瞄器等，減少缺貨訂單。"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Remaining demand available at"
msgstr "剩餘需求可於"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order_line.py:0
msgid "Replenish on Order (MTO)"
msgstr "訂單補給（特別訂製）"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "Reserved"
msgstr "已保留"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_return_picking
msgid "Return Picking"
msgstr "退回揀貨"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Returns"
msgstr "退回"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__route_id
msgid "Route"
msgstr "路線"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_move__sale_line_id
msgid "Sale Line"
msgstr "銷售資料行"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_procurement_group__sale_id
msgid "Sale Order"
msgstr "銷售訂單"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "Sale Orders"
msgstr "銷售訂單"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_lot__sale_order_count
msgid "Sale order count"
msgstr "銷售訂單個數"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_report
msgid "Sales Analysis Report"
msgstr "銷售分析報告"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order
#: model:ir.model.fields,field_description:sale_stock.field_stock_picking__sale_id
msgid "Sales Order"
msgstr "銷售訂單"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "銷售訂單取消"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "銷售訂單資料行"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_location_route_view_form_inherit_sale_stock
msgid "Sales Order Lines"
msgstr "銷售訂單資料行"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_lot__sale_order_ids
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_stock_help_message_template
msgid "Sales Orders"
msgstr "銷售訂單"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_company__security_lead
msgid "Sales Safety Days"
msgstr "銷售安全天數"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Schedule deliveries earlier to avoid delays"
msgstr "提前安排交貨以避免延誤"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__scheduled_date
msgid "Scheduled Date"
msgstr "預定日期"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__security_lead
msgid "Security Lead Time"
msgstr "安全時間"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__use_security_lead
msgid "Security Lead Time for Sales"
msgstr "銷售的安全提前期"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_route__sale_selectable
msgid "Selectable on Sales Order Line"
msgstr "在銷售訂單行上可選"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__one
msgid "Ship all products at once"
msgstr "一次運送所有產品"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__direct
msgid "Ship products as soon as available, with back orders"
msgstr "盡快運送產品，並退回訂單"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_policy
msgid "Shipping Policy"
msgstr "交貨策略"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__delivery_status__started
msgid "Started"
msgstr "已發貨"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move
msgid "Stock Move"
msgstr "庫存移動"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__move_ids
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order_line__qty_delivered_method__stock_move
msgid "Stock Moves"
msgstr "庫存移動"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "庫存補貨報告"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rule
msgid "Stock Rule"
msgstr "庫存規則"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "庫存規則報告"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "庫存規則報告"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/xml/delay_alert.xml:0
msgid "The delivery"
msgstr "交貨"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
msgid ""
"The delivery address has been changed on the Sales Order<br/>\n"
"                        From <strong>\"%(old_address)s\"</strong> to <strong>\"%(new_address)s\"</strong>,\n"
"                        You should probably update the partner on this document."
msgstr ""
"銷售訂單上的送貨地址已更改，<br/>\n"
"                        從「<strong>%(old_address)s</strong>」至「<strong>%(new_address)s</strong>」，\n"
"                        你可能需要更新此文件上的合作夥伴。"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/product_catalog/sale_order_line/sale_order_line.js:0
msgid ""
"The ordered quantity cannot be decreased below the amount already delivered."
" Instead, create a return in your inventory."
msgstr "訂購數量不可低於已交貨數量。請改為在庫存中建立退貨。"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order_line.py:0
msgid ""
"The ordered quantity of a sale order line cannot be decreased below the "
"amount already delivered. Instead, create a return in your inventory."
msgstr "銷售訂單行的訂購數量不能低於已交付的數量。相反，應在庫存中創建退貨。"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "This product is replenished on demand."
msgstr "本產品可按需求補充。"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__is_storable
msgid "Track Inventory"
msgstr "追蹤庫存"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_picking
msgid "Transfer"
msgstr "轉移"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_ids
msgid "Transfers"
msgstr "調撥"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_users
msgid "User"
msgstr "使用者"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/widgets/qty_at_date_widget.xml:0
msgid "View Forecast"
msgstr "查看預測"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__virtual_available_at_date
msgid "Virtual Available At Date"
msgstr "虛擬可用日期"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_warehouse
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_report__warehouse_id
msgid "Warehouse"
msgstr "倉庫"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
msgid "Warning!"
msgstr "警告！"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__one
msgid "When all products are ready"
msgstr "當所有產品就緒時"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "When to start shipping"
msgstr "何時開始裝運"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
msgid ""
"You must have a warehouse for line using a delivery in different company."
msgstr "若有以其他公司使用送貨服務的資料行，必須設立一個倉庫。"

#. module: sale_stock
#. odoo-python
#: code:addons/sale_stock/models/sale_order.py:0
msgid "You must set a warehouse on your sale order to proceed."
msgstr "銷售單必須設定倉庫，才可繼續。"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "cancelled"
msgstr "已取消"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "天"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "of"
msgstr "的"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "ordered instead of"
msgstr "替代採購"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "processed instead of"
msgstr "已做替代處理"

#. module: sale_stock
#. odoo-javascript
#: code:addons/sale_stock/static/src/xml/delay_alert.xml:0
msgid "will be late."
msgstr "將逾期"
