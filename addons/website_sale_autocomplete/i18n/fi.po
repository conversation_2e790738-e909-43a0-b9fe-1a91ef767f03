# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_autocomplete
# 
# Translators:
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2024
# <PERSON><PERSON> <ossi.manty<PERSON><PERSON>@obs-solutions.fi>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> (RMO) <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON> (RMO) <<EMAIL>>, 2024\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_autocomplete
#: model_terms:ir.ui.view,arch_db:website_sale_autocomplete.res_config_settings_view_form_inherit_autocomplete_googleplaces
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Create a Google Project and get a key"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Luo Google-projekti ja hanki avain"

#. module: website_sale_autocomplete
#: model_terms:ir.ui.view,arch_db:website_sale_autocomplete.res_config_settings_view_form_inherit_autocomplete_googleplaces
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Enable billing on your Google Project"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Ota laskutus käyttöön Google-projektissasi"

#. module: website_sale_autocomplete
#: model_terms:ir.ui.view,arch_db:website_sale_autocomplete.res_config_settings_view_form_inherit_autocomplete_googleplaces
msgid "API Key"
msgstr "API Avain"

#. module: website_sale_autocomplete
#: model:ir.model,name:website_sale_autocomplete.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: website_sale_autocomplete
#: model:ir.model.fields,field_description:website_sale_autocomplete.field_res_config_settings__google_places_api_key
#: model:ir.model.fields,field_description:website_sale_autocomplete.field_website__google_places_api_key
msgid "Google Places API Key"
msgstr "Google Places API-avain"

#. module: website_sale_autocomplete
#. odoo-javascript
#: code:addons/website_sale_autocomplete/static/src/xml/autocomplete.xml:0
msgid "Powered by Google"
msgstr "Järjestelmää pyörittää Google"

#. module: website_sale_autocomplete
#: model:ir.model,name:website_sale_autocomplete.model_website
msgid "Website"
msgstr "Verkkosivu"
