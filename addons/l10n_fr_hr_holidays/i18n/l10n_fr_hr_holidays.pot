# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_fr_hr_holidays
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.3\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-05-16 14:00+0000\n"
"PO-Revision-Date: 2024-05-16 14:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_fr_hr_holidays
#. odoo-python
#: code:addons/l10n_fr_hr_holidays/models/hr_leave.py:0
msgid ""
"An employee can't take paid time off in a period without any work hours."
msgstr ""

#. module: l10n_fr_hr_holidays
#: model:ir.model,name:l10n_fr_hr_holidays.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_fr_hr_holidays
#: model:ir.model.fields,field_description:l10n_fr_hr_holidays.field_res_company__l10n_fr_reference_leave_type
#: model:ir.model.fields,field_description:l10n_fr_hr_holidays.field_res_config_settings__l10n_fr_reference_leave_type
msgid "Company Paid Time Off Type"
msgstr ""

#. module: l10n_fr_hr_holidays
#: model:ir.model,name:l10n_fr_hr_holidays.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_fr_hr_holidays
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_holidays.res_config_settings_view_form
msgid "French Time Off Localization"
msgstr ""

#. module: l10n_fr_hr_holidays
#: model:hr.leave.type,name:l10n_fr_hr_holidays.l10n_fr_holiday_status_cl
msgid "Paid Time Off"
msgstr ""

#. module: l10n_fr_hr_holidays
#: model:ir.model,name:l10n_fr_hr_holidays.model_resource_calendar
msgid "Resource Working Time"
msgstr ""

#. module: l10n_fr_hr_holidays
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_holidays.res_config_settings_view_form
msgid ""
"Set the time off type used as the company Paid Time Off to compute part-"
"timers leave duration"
msgstr ""

#. module: l10n_fr_hr_holidays
#: model:ir.ui.menu,name:l10n_fr_hr_holidays.hr_holidays_menu_configuration
msgid "Settings"
msgstr ""

#. module: l10n_fr_hr_holidays
#: model:ir.model,name:l10n_fr_hr_holidays.model_hr_leave
msgid "Time Off"
msgstr ""

#. module: l10n_fr_hr_holidays
#. odoo-python
#: code:addons/l10n_fr_hr_holidays/models/res_company.py:0
msgid "You must first define a reference time off type for the company."
msgstr ""
