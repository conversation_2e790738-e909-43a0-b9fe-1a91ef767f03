<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_provider_form" model="ir.ui.view">
        <field name="name">Authorize.Net Provider Form</field>
        <field name="model">payment.provider</field>
        <field name="inherit_id" ref="payment.payment_provider_form"/>
        <field name="arch" type="xml">
            <group name="provider_credentials" position='inside'>
                <group invisible="code != 'authorize'">
                    <field name="authorize_login" required="code == 'authorize' and state != 'disabled'"/>
                    <field name="authorize_transaction_key" password="True" required="code == 'authorize' and state != 'disabled'"/>
                    <field name="authorize_signature_key" password="True" required="code == 'authorize' and state != 'disabled'"/>
                    <label for="authorize_client_key"/>
                    <div class="o_row" col="2">
                        <field name="authorize_client_key"/>
                        <button class="oe_link" icon="fa-refresh" type="object"
                                name="action_update_merchant_details"
                                string="Generate Client Key"/>
                    </div>
                    <widget colspan="2" name="documentation_link" path="/applications/finance/payment_providers/authorize.html" label="How to get paid with Authorize.Net"/>
                </group>
            </group>
            <div name="available_currencies" position="inside">
                <button string="Set Account Currency"
                        type="object"
                        name="action_update_merchant_details"
                        invisible="code != 'authorize'"
                        icon="fa-refresh"
                        class="oe_link"/>
            </div>
        </field>
    </record>

</odoo>
