# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_authorize
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"Language: sr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid ""
"<select id=\"o_authorize_account_type\" required=\"\" class=\"form-"
"select\">\n"
"                        <option value=\"checking\">Personal Checking</"
"option>\n"
"                        <option value=\"savings\">Personal Savings</option>\n"
"                        <option value=\"businessChecking\">Business "
"Checking</option>\n"
"                    </select>"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "ABA Routing Number"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_client_key
msgid "API Client Key"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_login
msgid "API Login ID"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_signature_key
msgid "API Signature Key"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_transaction_key
msgid "API Transaction Key"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Account Number"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_provider__code__authorize
msgid "Authorize.Net"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_profile
msgid "Authorize.Net Profile ID"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Account Type"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Name"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Code"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Number"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__code
msgid "Code"
msgstr ""

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
msgid ""
"Could not fetch merchant details:\n"
"%s"
msgstr ""

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid ""
"Could not retrieve the transaction details. (error code: %s; error_details: "
"%s)"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Expiration"
msgstr ""

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
msgid ""
"Failed to authenticate.\n"
"%s"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_provider_form
msgid "Generate Client Key"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_provider_form
msgid "How to get paid with Authorize.Net"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "MM"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Name On Account"
msgstr ""

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
msgid "Only one currency can be selected by Authorize.Net account."
msgstr ""

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_provider
msgid "Payment Provider"
msgstr ""

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_token
msgid "Payment Token"
msgstr ""

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_authorize
#. odoo-javascript
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr ""

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid ""
"Received data with status code \"%(status)s\" and error code \"%(error)s\""
msgstr ""

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/controllers/main.py:0
msgid "Received tampered payment request data."
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_provider_form
msgid "Set Account Currency"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_provider__authorize_login
msgid "The ID solely used to identify the account with Authorize.Net"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_provider__authorize_client_key
msgid ""
"The public client key. To generate directly from Odoo or from Authorize.Net "
"backend."
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid ""
"The transaction is not in a status to be refunded. (status: %s, details: %s)"
msgstr ""

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_profile
msgid ""
"The unique reference for the partner/token combination in the Authorize.net "
"backend."
msgstr ""

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
msgid "This action cannot be performed while the provider is disabled."
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "YY"
msgstr ""
