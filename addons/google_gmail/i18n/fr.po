# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_gmail
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid "<i class=\"fa fa-cog\" title=\"Edit Settings\"/>"
msgstr "<i class=\"fa fa-cog\" title=\"Modifier les paramètres\"/>"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                        Connect your Gmail account"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                        Connecter votre compte Gmail"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.fetchmail_server_view_form
msgid ""
"<span invisible=\"server_type != 'gmail' or not google_gmail_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Gmail Token Valid\n"
"                    </span>"
msgstr ""
"<span invisible=\"server_type != 'gmail' or not google_gmail_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Jeton Gmail valide\n"
"                    </span>"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"<span invisible=\"smtp_authentication != 'gmail' or not google_gmail_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Gmail Token Valid\n"
"                    </span>"
msgstr ""
"<span invisible=\"smtp_authentication != 'gmail' or not google_gmail_refresh_token\" class=\"badge text-bg-success\">\n"
"                        Jeton Gmail valide\n"
"                    </span>"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_access_token
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_access_token
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_access_token
msgid "Access Token"
msgstr "Jeton d'accès"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_access_token_expiration
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_access_token_expiration
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_access_token_expiration
msgid "Access Token Expiration Timestamp"
msgstr "Horodatage de l'expiration du jeton d'accès"

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/controllers/main.py:0
msgid "An error occur during the authentication process."
msgstr "Une erreur s'est produite pendant le processus d'authentification."

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
msgid "An error occurred when fetching the access token."
msgstr "Une erreur s'est produite lors de la récupération du jeton d'accès."

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__smtp_authentication
msgid "Authenticate with"
msgstr "S'authentifier avec"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_authorization_code
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_authorization_code
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_authorization_code
msgid "Authorization Code"
msgstr "Code d'autorisation"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/fetchmail_server.py:0
msgid ""
"Connect your Gmail account with the OAuth Authentication process. \n"
"You will be redirected to the Gmail login page where you will need to accept the permission."
msgstr ""
"Connectez votre compte Gmail grâce au processus d'authentification OAuth.\n"
"Vous serez redirigé vers la page de connexion Gmail où vous devrez accepter l'autorisation."

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/ir_mail_server.py:0
msgid ""
"Connect your Gmail account with the OAuth Authentication process.  \n"
"By default, only a user with a matching email address will be able to use this server. To extend its use, you should set a \"mail.default.from\" system parameter."
msgstr ""
"Connectez votre compte Gmail grâce au processus d'authentification OAuth.\n"
"Par défaut, seul un utilisateur avec une adresse e-mail correspondante pourra utiliser ce serveur. Pour étendre son utilisation, vous devriez définir un paramètre système \"mail.default.from\"."

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_res_config_settings__google_gmail_client_identifier
msgid "Gmail Client Id"
msgstr "Id Client Gmail"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_res_config_settings__google_gmail_client_secret
msgid "Gmail Client Secret"
msgstr "Secret Client Gmail"

#. module: google_gmail
#: model:ir.model.fields.selection,name:google_gmail.selection__fetchmail_server__server_type__gmail
#: model:ir.model.fields.selection,name:google_gmail.selection__ir_mail_server__smtp_authentication__gmail
msgid "Gmail OAuth Authentication"
msgstr "Authentification OAuth Gmail"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_google_gmail_mixin
msgid "Google Gmail Mixin"
msgstr "Mixin Google Gmail"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "ID"
msgstr "ID"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "ID of your Google app"
msgstr "ID de votre app Google"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_fetchmail_server
msgid "Incoming Mail Server"
msgstr "Serveur de messagerie entrant"

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/ir_mail_server.py:0
msgid ""
"Incorrect Connection Security for Gmail mail server “%s”. Please set it to "
"\"TLS (STARTTLS)\"."
msgstr ""
"Sécurité de connexion incorrecte pour le serveur de messagerie Gmail “%s”. "
"Veuillez le définir sur \"TLS (STARTTLS)\"."

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_ir_mail_server
msgid "Mail Server"
msgstr "Serveur de messagerie"

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
msgid "Only the administrator can link a Gmail mail server."
msgstr "Seul l'administrateur peut lier un serveur de messagerie Gmail."

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
msgid "Please configure your Gmail credentials."
msgstr "Veuillez configurer vos identifiants Gmail."

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/ir_mail_server.py:0
msgid ""
"Please fill the \"Username\" field with your Gmail username (your email "
"address). This should be the same account as the one used for the Gmail "
"OAuthentication Token."
msgstr ""
"Veuillez compléter votre nom d'utilisateur Gmail (votre adresse e-mail) dans"
" le champ \"Nom d'utilisateur\". Ceci devrait être le même compte que celui "
"utilisé pour le jeton OAuthentification Gmail."

#. module: google_gmail
#. odoo-python
#: code:addons/google_gmail/models/ir_mail_server.py:0
msgid ""
"Please leave the password field empty for Gmail mail server “%s”. The OAuth "
"process does not require it"
msgstr ""
"Veuillez laisser le champ du mot de passe vide pour le serveur de messagerie"
" Gmail “%s”. Le processus OAuth ne l'exige pas"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid "Read More"
msgstr "Plus d'info"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_refresh_token
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_refresh_token
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_refresh_token
msgid "Refresh Token"
msgstr "Actualiser le jeton"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Secret"
msgstr "Secret"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Secret of your Google app"
msgstr "Secret de votre app Google"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "Type de serveur"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"Setup your Gmail API credentials in the general settings to link a Gmail "
"account."
msgstr ""
"Configurez vos informations d'identification de l'API Gmail dans les "
"paramètres généraux pour lier un compte Gmail."

#. module: google_gmail
#: model:ir.model.fields,help:google_gmail.field_fetchmail_server__google_gmail_uri
#: model:ir.model.fields,help:google_gmail.field_google_gmail_mixin__google_gmail_uri
#: model:ir.model.fields,help:google_gmail.field_ir_mail_server__google_gmail_uri
msgid "The URL to generate the authorization code from Google"
msgstr "L'URL pour générer le code d'autorisation de Google"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_fetchmail_server__google_gmail_uri
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_uri
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_uri
msgid "URI"
msgstr "URI"
