<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="600" height="600">
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"/>
        </clipPath>
        <path id="filterPath" d="M0.3886,0C0.365,0,0.3458,0.0191,0.3458,0.0427V0.9573C0.3458,0.9809,0.365,1,0.3886,1H0.6114C0.635,1,0.6542,0.9809,0.6542,0.9573V0.0427C0.6542,0.0191,0.635,0,0.6114,0H0.3886ZM0,0.079C0,0.0576,0.0158,0.0395,0.037,0.0367L0.2599,0.0065C0.2855,0.0031,0.3083,0.023,0.3083,0.0489V0.9511C0.3083,0.977,0.2855,0.9969,0.2599,0.9935L0.037,0.9633C0.0158,0.9605,0,0.9424,0,0.921V0.079ZM1,0.9253C1,0.9467,0.9842,0.9648,0.9631,0.9677L0.7444,0.9976C0.7187,1.0011,0.6958,0.9812,0.6958,0.9553L0.6958,0.0533C0.6958,0.0274,0.7187,0.0074,0.7444,0.0109L0.9631,0.0409C0.9842,0.0438,1,0.0619,1,0.0832L1,0.9253Z">
        </path>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"/>
</svg>
