# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_account
# 
# Translators:
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: <PERSON> (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: microsoft_account
#. odoo-python
#: code:addons/microsoft_account/models/microsoft_service.py:0
msgid "Method not supported [%s] not in [GET, POST, PUT, PATCH or DELETE]!"
msgstr "Metode ikke støttet [%s] ikke i [GET, POST, PUT, PATCH or DELETE]!"

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_rtoken
msgid "Microsoft Refresh Token"
msgstr ""

#. module: microsoft_account
#: model:ir.model,name:microsoft_account.model_microsoft_service
msgid "Microsoft Service"
msgstr ""

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_token_validity
msgid "Microsoft Token Validity"
msgstr ""

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_token
msgid "Microsoft User token"
msgstr ""

#. module: microsoft_account
#. odoo-python
#: code:addons/microsoft_account/models/microsoft_service.py:0
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid"
msgstr ""

#. module: microsoft_account
#. odoo-python
#: code:addons/microsoft_account/models/microsoft_service.py:0
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired"
msgstr ""

#. module: microsoft_account
#: model:ir.model,name:microsoft_account.model_res_users
msgid "User"
msgstr "Bruker"
