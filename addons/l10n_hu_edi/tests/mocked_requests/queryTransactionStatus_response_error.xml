<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ns2:QueryTransactionStatusResponse xmlns="http://schemas.nav.gov.hu/NTCA/1.0/common"
    xmlns:ns2="http://schemas.nav.gov.hu/OSA/3.0/api"
    xmlns:ns3="http://schemas.nav.gov.hu/OSA/3.0/base"
    xmlns:ns4="http://schemas.nav.gov.hu/OSA/3.0/data">
    <header>
        <requestId>ODOO218e3a9fa6294341bbcd00bc65</requestId>
        <timestamp>2023-09-28T19:00:27.670Z</timestamp>
        <requestVersion>3.0</requestVersion>
        <headerVersion>1.0</headerVersion>
    </header>
    <result>
        <funcCode>OK</funcCode>
    </result>
    <ns2:software>
        <ns2:softwareId>ODOOENTERPRISE-160</ns2:softwareId>
        <ns2:softwareName>Odoo Enterprise</ns2:softwareName>
        <ns2:softwareOperation>LOCAL_SOFTWARE</ns2:softwareOperation>
        <ns2:softwareMainVersion>16.0+e</ns2:softwareMainVersion>
        <ns2:softwareDevName>OdooTech Zrt.</ns2:softwareDevName>
        <ns2:softwareDevContact><EMAIL></ns2:softwareDevContact>
        <ns2:softwareDevCountryCode>HU</ns2:softwareDevCountryCode>
        <ns2:softwareDevTaxNumber>32226375</ns2:softwareDevTaxNumber>
    </ns2:software>
    <ns2:processingResults>
        <ns2:processingResult>
            <ns2:index>1</ns2:index>
            <ns2:invoiceStatus>ABORTED</ns2:invoiceStatus>
            <ns2:technicalValidationMessages>
                <validationResultCode>ERROR</validationResultCode>
                <validationErrorCode>SCHEMA_VIOLATION</validationErrorCode>
                <message>XML contains on line: [2] and column: [1,018] error: [cvc-pattern-valid:
                    Value '****************' is not facet-valid with respect to pattern
                    '[0-9]{8}[-][0-9]{8}[-][0-9]{8}|[0-9]{8}[-][0-9]{8}|[A-Z]{2}[0-9]{2}[0-9A-Za-z]{11,30}'
                    for type 'BankAccountNumberType'.]</message>
            </ns2:technicalValidationMessages>
            <ns2:technicalValidationMessages>
                <validationResultCode>ERROR</validationResultCode>
                <validationErrorCode>SCHEMA_VIOLATION</validationErrorCode>
                <message>XML contains on line: [2] and column: [1,018] error: [cvc-type.3.1.3: The
                    value '****************' of element 'supplierBankAccountNumber' is not valid.]</message>
            </ns2:technicalValidationMessages>
            <ns2:technicalValidationMessages>
                <validationResultCode>ERROR</validationResultCode>
                <validationErrorCode>SCHEMA_VIOLATION</validationErrorCode>
                <message>XML contains on line: [2] and column: [1,831] error: [cvc-type.3.1.1:
                    Element 'paymentMethod' is a simple type, so it cannot have attributes,
                    excepting those whose namespace name is identical to
                    'http://www.w3.org/2001/XMLSchema-instance' and whose [local name] is one of
                    'type', 'nil', 'schemaLocation' or 'noNamespaceSchemaLocation'. However, the
                    attribute, 'if' was found.]</message>
            </ns2:technicalValidationMessages>
            <ns2:technicalValidationMessages>
                <validationResultCode>ERROR</validationResultCode>
                <validationErrorCode>SCHEMA_VIOLATION</validationErrorCode>
                <message>Xml validation failed</message>
            </ns2:technicalValidationMessages>
            <ns2:compressedContentIndicator>false</ns2:compressedContentIndicator>
        </ns2:processingResult>
        <ns2:originalRequestVersion>3.0</ns2:originalRequestVersion>
    </ns2:processingResults>
</ns2:QueryTransactionStatusResponse>