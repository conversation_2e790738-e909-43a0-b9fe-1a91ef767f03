# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting_dropshipping
# 
# Translators:
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,help:mrp_subcontracting_dropshipping.field_purchase_order__default_location_dest_id_is_subcontracting_loc
msgid ""
"Check this box to create a new dedicated subcontracting location for this "
"company. Note that standard subcontracting routes will be adapted so as to "
"take these into account automatically."
msgstr ""
"Markera denna ruta för att skapa en ny särskild underleverantörsplats för "
"detta företag. Observera att standardrutterna för underleverantörer kommer "
"att anpassas så att de automatiskt tar hänsyn till dessa."

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_res_company
msgid "Companies"
msgstr "Företag"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,field_description:mrp_subcontracting_dropshipping.field_res_company__dropship_subcontractor_pick_type_id
msgid "Dropship Subcontractor Pick Type"
msgstr "Dropship Underleverantör välj typ"

#. module: mrp_subcontracting_dropshipping
#. odoo-python
#: code:addons/mrp_subcontracting_dropshipping/models/stock_warehouse.py:0
#: model:stock.route,name:mrp_subcontracting_dropshipping.route_subcontracting_dropshipping
msgid "Dropship Subcontractor on Order"
msgstr "Dropship Underleverantör på beställning"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,field_description:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_to_resupply
msgid "Dropship Subcontractors"
msgstr "Underleverantörer till Dropship"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,help:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_to_resupply
msgid "Dropship subcontractors with components"
msgstr "Dropship-underleverantörer med komponenter"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,field_description:mrp_subcontracting_dropshipping.field_purchase_order__default_location_dest_id_is_subcontracting_loc
msgid "Is a Subcontracting Location?"
msgstr "Är en underleverantörsplats?"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Minsta lagerregel"

#. module: mrp_subcontracting_dropshipping
#. odoo-python
#: code:addons/mrp_subcontracting_dropshipping/models/purchase.py:0
msgid "Please note this purchase order is for subcontracting purposes."
msgstr "Observera att denna inköpsorder är avsedd för underleverantörer."

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr "Mixin för produktpåfyllning"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_purchase_order
msgid "Purchase Order"
msgstr "Inköpsorder"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_move
msgid "Stock Move"
msgstr "Lagerflytt"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_rule
msgid "Stock Rule"
msgstr "Lagerregel"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,field_description:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_pull_id
msgid "Subcontracting-Dropshipping MTS Rule"
msgstr "Underleverantörer-Dropshipping MTS-regel"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_picking
msgid "Transfer"
msgstr "Överföring"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_warehouse
msgid "Warehouse"
msgstr "Lager"

#. module: mrp_subcontracting_dropshipping
#. odoo-python
#: code:addons/mrp_subcontracting_dropshipping/models/purchase.py:0
msgid "Warning"
msgstr "Varning"
