<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="msar_external_layout_boxed" inherit_id="web.external_layout_boxed">
        <xpath expr="//div[contains(@class, 'o_footer_content')]" position="replace">
            <div class="o_footer_content row border-top pt-2">
                <div class="col-12 text-center">
                    <span t-field="company.report_footer"/>
                </div>
<!--                <span t-if="report_type == 'pdf'" class="text-muted">Page <span class="page"/> / <span class="topage"/></span>-->
            </div>
        </xpath>
        <xpath expr="//div[contains(@class, 'row mb8')]" position="replace">
            <div class="row mb8">
                <div class="col-8" t-if="o and o._name == 'sale.order'">
                    <img t-if="company.logo" class="o_company_logo_big" t-att-src="image_data_uri(company.logo)" alt="Logo"/>
                    <div style="font-size: 15px; line-height: 1.2;">
                        <span t-field="company.name" style="font-weight: 900;"/>
                        <div t-if="company.street">
                            <span>PO BOX:<span t-field="company.po_box"/>, <span t-field="company.city"/> <span t-field="company.zip"/> Tel: <span t-field="company.phone"/> - fax: <span t-field="company.fax"/> </span>
                        </div>
                        <div>
                            <span>Email: <span t-field="company.email"/></span>
                        </div>
                        <div>
                            <span t-field="company.website"/>
                        </div>
                    </div>
                </div>
                <div class="col-4" t-if="o and o._name == 'sale.order'">
                    <h3 style="font-weight: 900; font-style: italic; font-size: 24px;">QUOTATION</h3>
                    <table class="table table-borderless" style="font-size: 14px; width: auto;">
                        <tbody>
                            <tr>
                                <td style="font-weight: bold;padding: 0; width: 100px;">Quote No:</td>
                                <td style="padding: 3px 5px; width: 150px;"><span t-field="doc.name"/></td>
                            </tr>
                            <tr>
                                <td style="font-weight: bold;padding: 0; width: 100px;">Quote Date:</td>
                                <td style="padding: 3px 5px; width: 150px;"><span t-field="doc.date_order" t-options='{"widget": "date"}'/></td>
                            </tr>
                            <tr>
                                <td style="font-weight: bold;padding: 0; width: 100px;">Offer Validity:</td>
                                <td style="padding: 3px 5px; width: 150px;"><span t-field="doc.validity_date" t-options='{"widget": "date"}'/></td>
                            </tr>
                            <tr>
                                <td style="font-weight: bold;padding: 0; width: 100px;">Prepared By:</td>
                                <td style="padding: 3px 5px; width: 150px;"><span t-field="doc.prepare_user_id.name"/></td>
                            </tr>
                            <tr>
                                <td style="font-weight: bold;padding: 0; width: 100px;">Sales Name:</td>
                                <td style="padding: 3px 5px; width: 150px;"><span t-field="doc.user_id.name"/></td>
                            </tr>
                            <tr>
                                <td style="font-weight: bold;padding: 0; width: 100px;">Sales Code:</td>
                                <td style="padding: 3px 5px; width: 150px;"><span t-field="doc.user_id.employee_id.identification_id"/></td>
                            </tr>
                            <tr>
                                <td style="font-weight: bold;padding: 0; width: 100px;">Currency:</td>
                                <td style="padding: 3px 5px; width: 150px;"><span t-field="doc.currency_id.name"/></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-4" t-if="o and o._name == 'account.move'">
                    <img t-if="company.logo" class="o_company_logo_big" t-att-src="image_data_uri(company.logo)" alt="Logo"/>
                </div>
                <div class="col-8 text-end mb4" t-if="o and o._name == 'account.move'">
                    <div style="font-size: 20px; font-weight: 900; margin-bottom: 8px;">
                        <span t-field="company.name" style="font-weight: 900;"/>
                    </div>
                    <div t-if="company._fields.get('arabic_name') and company.arabic_name" style="font-size: 18px; font-weight: 900; margin-bottom: 8px; direction: rtl;">
                        <span t-field="company.arabic_name" style="font-weight: 900;"/>
                    </div>
                    <div t-if="(company._fields.get('cr_number') and company.cr_number) or (company._fields.get('cc_number') and company.cc_number)" style="font-size: 14px; margin-bottom: 5px;">
                        <div style="display: flex; justify-content: flex-end; align-items: center;">
                            <span>
                                <t t-if="company._fields.get('cr_number') and company.cr_number">
                                    <span>C.R.</span><span t-field="company.cr_number"/>
                                </t>
                                <span t-if="company._fields.get('cr_number') and company.cr_number and company._fields.get('cc_number') and company.cc_number"> - </span>
                                <t t-if="company._fields.get('cc_number') and company.cc_number">
                                    <span>C.C.NO.</span><span t-field="company.cc_number"/>
                                </t>
                            </span>
                            <span style="margin: 0 10px;"></span>
                            <span style="direction: rtl; font-family: 'Tajawal', 'Amiri', sans-serif;">
                                <t t-if="company._fields.get('cr_number') and company.cr_number">
                                    <span>س.ت:</span><span t-field="company.cr_number"/>
                                </t>
                                <span t-if="company._fields.get('cr_number') and company.cr_number and company._fields.get('cc_number') and company.cc_number">-</span>
                                <t t-if="company._fields.get('cc_number') and company.cc_number">
                                    <span>رقماشراكالغرفةالتجارية</span><span t-field="company.cc_number"/>
                                </t>
                            </span>
                        </div>
                    </div>
                    <div name="company_address" class="float-end mb4">
                    </div>
                </div>
            </div>
        </xpath>
    </template>
</odoo>