# -*- coding: utf-8 -*-

from odoo import api, fields, models


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    discount_amount = fields.Float(string='Discount Amount', compute='_compute_discount_amount', store=True)

    @api.depends('price_unit', 'quantity', 'discount')
    def _compute_discount_amount(self):
        for line in self:
            if line.discount:
                line.discount_amount = line.price_unit * line.quantity * (line.discount / 100.0)
            else:
                line.discount_amount = 0.0
